{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/SaaS%20Course/Augment/trekInn/frontend/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nexport default api;\n\n// API endpoints\nexport const authAPI = {\n  register: (data: { name: string; email: string; password: string; password_confirmation: string }) =>\n    api.post('/auth/register', data),\n  login: (data: { email: string; password: string }) =>\n    api.post('/auth/login', data),\n  logout: () => api.post('/auth/logout'),\n  getUser: () => api.get('/auth/user'),\n};\n\nexport const trekAPI = {\n  getAll: (params?: Record<string, unknown>) => api.get('/treks', { params }),\n  getById: (id: string) => api.get(`/treks/${id}`),\n  create: (data: Record<string, unknown>) => api.post('/treks', data),\n  update: (id: string, data: Record<string, unknown>) => api.put(`/treks/${id}`, data),\n  delete: (id: string) => api.delete(`/treks/${id}`),\n};\n\nexport const bookingAPI = {\n  getAll: (params?: Record<string, unknown>) => api.get('/bookings', { params }),\n  getById: (id: string) => api.get(`/bookings/${id}`),\n  create: (data: Record<string, unknown>) => api.post('/bookings', data),\n  update: (id: string, data: Record<string, unknown>) => api.put(`/bookings/${id}`, data),\n  cancel: (id: string) => api.delete(`/bookings/${id}`),\n};\n"], "names": [], "mappings": ";;;;;;AAEqB;AAFrB;;AAEA,MAAM,eAAe,iEAAmC;AAExD,wBAAwB;AACxB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,wCAAwC;AACxC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,6CAA6C;AAC7C,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa;AAGR,MAAM,UAAU;IACrB,UAAU,CAAC,OACT,IAAI,IAAI,CAAC,kBAAkB;IAC7B,OAAO,CAAC,OACN,IAAI,IAAI,CAAC,eAAe;IAC1B,QAAQ,IAAM,IAAI,IAAI,CAAC;IACvB,SAAS,IAAM,IAAI,GAAG,CAAC;AACzB;AAEO,MAAM,UAAU;IACrB,QAAQ,CAAC,SAAqC,IAAI,GAAG,CAAC,UAAU;YAAE;QAAO;IACzE,SAAS,CAAC,KAAe,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI;IAC/C,QAAQ,CAAC,OAAkC,IAAI,IAAI,CAAC,UAAU;IAC9D,QAAQ,CAAC,IAAY,OAAkC,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;IAC/E,QAAQ,CAAC,KAAe,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI;AACnD;AAEO,MAAM,aAAa;IACxB,QAAQ,CAAC,SAAqC,IAAI,GAAG,CAAC,aAAa;YAAE;QAAO;IAC5E,SAAS,CAAC,KAAe,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;IAClD,QAAQ,CAAC,OAAkC,IAAI,IAAI,CAAC,aAAa;IACjE,QAAQ,CAAC,IAAY,OAAkC,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;IAClF,QAAQ,CAAC,KAAe,IAAI,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;AACtD", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/SaaS%20Course/Augment/trekInn/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User, AuthResponse } from '@/types';\nimport { authAPI } from '@/lib/api';\n\ninterface AuthContextType {\n  user: User | null;\n  token: string | null;\n  login: (email: string, password: string) => Promise<void>;\n  register: (name: string, email: string, password: string, passwordConfirmation: string) => Promise<void>;\n  logout: () => void;\n  loading: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [token, setToken] = useState<string | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Check for existing auth data on mount\n    const savedToken = localStorage.getItem('auth_token');\n    const savedUser = localStorage.getItem('user');\n\n    if (savedToken && savedUser) {\n      setToken(savedToken);\n      setUser(JSON.parse(savedUser));\n    }\n    setLoading(false);\n  }, []);\n\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await authAPI.login({ email, password });\n      const data: AuthResponse = response.data;\n\n      setUser(data.user);\n      setToken(data.token);\n\n      localStorage.setItem('auth_token', data.token);\n      localStorage.setItem('user', JSON.stringify(data.user));\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  const register = async (name: string, email: string, password: string, passwordConfirmation: string) => {\n    try {\n      const response = await authAPI.register({\n        name,\n        email,\n        password,\n        password_confirmation: passwordConfirmation,\n      });\n      const data: AuthResponse = response.data;\n\n      setUser(data.user);\n      setToken(data.token);\n\n      localStorage.setItem('auth_token', data.token);\n      localStorage.setItem('user', JSON.stringify(data.user));\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  const logout = async () => {\n    try {\n      if (token) {\n        await authAPI.logout();\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setUser(null);\n      setToken(null);\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user');\n    }\n  };\n\n  const value = {\n    user,\n    token,\n    login,\n    register,\n    logout,\n    loading,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAeA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,wCAAwC;YACxC,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,MAAM,YAAY,aAAa,OAAO,CAAC;YAEvC,IAAI,cAAc,WAAW;gBAC3B,SAAS;gBACT,QAAQ,KAAK,KAAK,CAAC;YACrB;YACA,WAAW;QACb;iCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAAE;gBAAO;YAAS;YACvD,MAAM,OAAqB,SAAS,IAAI;YAExC,QAAQ,KAAK,IAAI;YACjB,SAAS,KAAK,KAAK;YAEnB,aAAa,OAAO,CAAC,cAAc,KAAK,KAAK;YAC7C,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,KAAK,IAAI;QACvD,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,WAAW,OAAO,MAAc,OAAe,UAAkB;QACrE,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;gBACtC;gBACA;gBACA;gBACA,uBAAuB;YACzB;YACA,MAAM,OAAqB,SAAS,IAAI;YAExC,QAAQ,KAAK,IAAI;YACjB,SAAS,KAAK,KAAK;YAEnB,aAAa,OAAO,CAAC,cAAc,KAAK,KAAK;YAC7C,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,KAAK,IAAI;QACvD,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,IAAI,OAAO;gBACT,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,QAAQ;YACR,SAAS;YACT,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GA7EgB;KAAA;AA+ET,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}