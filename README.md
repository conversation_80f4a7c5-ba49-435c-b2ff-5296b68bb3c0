# 🏔️ TrekInn - Trekking Website

A modern, full-stack trekking platform built with **Next.js** and **Laravel**.

## 🛠️ Tech Stack

### Frontend (Next.js)
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Custom components with Lucide React icons
- **State Management**: React hooks + Context API
- **HTTP Client**: Axios for API communication

### Backend (Laravel)
- **Framework**: Laravel 12
- **Database**: MySQL/PostgreSQL
- **Authentication**: Laravel Sanctum (API tokens)
- **API**: RESTful API architecture
- **File Storage**: Laravel Storage for images

## 📁 Project Structure

```
trekInn/
├── backend/              # Laravel API Backend
│   ├── app/
│   │   ├── Http/Controllers/Api/
│   │   ├── Models/
│   │   └── ...
│   ├── routes/api.php
│   ├── database/migrations/
│   └── ...
├── frontend/             # Next.js Frontend
│   ├── src/
│   │   ├── app/
│   │   ├── components/
│   │   ├── lib/
│   │   └── ...
│   └── ...
└── README.md
```

## 🚀 Features

### Core Features
- 🏔️ **Trek Catalog** - Browse and search trekking adventures
- 📅 **Booking System** - Reserve trek spots with date selection
- 👤 **User Authentication** - Secure login/registration
- 💳 **Payment Integration** - Ready for payment processing
- 📱 **Responsive Design** - Works on all devices
- 🔍 **SEO Optimized** - Server-side rendering with Next.js

### Admin Features
- 📊 **Dashboard** - Manage treks and bookings
- 👥 **User Management** - View and manage users
- 📈 **Analytics** - Track bookings and revenue

## 🏃‍♂️ Getting Started

### Prerequisites
- Node.js 18+ and npm
- PHP 8.1+ and Composer
- MySQL or PostgreSQL

### Backend Setup (Laravel)
```bash
cd backend
composer install
cp .env.example .env
php artisan key:generate
php artisan migrate
php artisan serve
```

### Frontend Setup (Next.js)
```bash
cd frontend
npm install
npm run dev
```

## 🌐 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout

### Treks
- `GET /api/treks` - List all treks
- `GET /api/treks/{id}` - Get trek details
- `POST /api/treks` - Create trek (admin)
- `PUT /api/treks/{id}` - Update trek (admin)

### Bookings
- `GET /api/bookings` - User's bookings
- `POST /api/bookings` - Create booking
- `PUT /api/bookings/{id}` - Update booking

## 🎨 Design System

Built with Tailwind CSS using a mountain-inspired color palette:
- **Primary**: Mountain blues and greens
- **Secondary**: Earth tones
- **Accent**: Sunset oranges
- **Typography**: Clean, modern fonts

## 📱 Responsive Breakpoints
- Mobile: 320px - 768px
- Tablet: 768px - 1024px
- Desktop: 1024px+

## 🔧 Development

### Backend Commands
```bash
php artisan migrate:fresh --seed  # Reset database
php artisan make:controller Api/TrekController --api
php artisan make:model Trek -m
```

### Frontend Commands
```bash
npm run dev          # Development server
npm run build        # Production build
npm run lint         # ESLint check
```

## 🚀 Deployment

### Backend (Laravel)
- Deploy to any PHP hosting (DigitalOcean, AWS, etc.)
- Configure environment variables
- Set up database and run migrations

### Frontend (Next.js)
- Deploy to Vercel (recommended)
- Configure environment variables
- Connect to backend API

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

---

Built with ❤️ for adventure seekers and mountain lovers!
