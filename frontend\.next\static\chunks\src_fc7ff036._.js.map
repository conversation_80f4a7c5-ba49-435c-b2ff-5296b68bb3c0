{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/SaaS%20Course/Augment/trekInn/frontend/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Mountain, LogOut, Menu, X } from 'lucide-react';\nimport { useState } from 'react';\n\nexport default function Navbar() {\n  const { user, logout } = useAuth();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <nav className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Mountain className=\"h-8 w-8 text-green-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">TrekInn</span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <Link\n              href=\"/\"\n              className=\"text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n            >\n              Home\n            </Link>\n            <Link\n              href=\"/treks\"\n              className=\"text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n            >\n              Treks\n            </Link>\n            {user ? (\n              <>\n                <Link\n                  href=\"/dashboard\"\n                  className=\"text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                >\n                  Dashboard\n                </Link>\n                <div className=\"flex items-center space-x-4\">\n                  <span className=\"text-gray-700 text-sm\">Welcome, {user.name}</span>\n                  <button\n                    onClick={handleLogout}\n                    className=\"flex items-center space-x-1 text-gray-700 hover:text-red-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span>Logout</span>\n                  </button>\n                </div>\n              </>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/login\"\n                  className=\"text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                >\n                  Login\n                </Link>\n                <Link\n                  href=\"/register\"\n                  className=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors\"\n                >\n                  Sign Up\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-gray-700 hover:text-green-600 p-2\"\n            >\n              {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t\">\n              <Link\n                href=\"/\"\n                className=\"text-gray-700 hover:text-green-600 block px-3 py-2 rounded-md text-base font-medium\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Home\n              </Link>\n              <Link\n                href=\"/treks\"\n                className=\"text-gray-700 hover:text-green-600 block px-3 py-2 rounded-md text-base font-medium\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Treks\n              </Link>\n              {user ? (\n                <>\n                  <Link\n                    href=\"/dashboard\"\n                    className=\"text-gray-700 hover:text-green-600 block px-3 py-2 rounded-md text-base font-medium\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Dashboard\n                  </Link>\n                  <div className=\"px-3 py-2\">\n                    <span className=\"text-gray-700 text-sm\">Welcome, {user.name}</span>\n                  </div>\n                  <button\n                    onClick={handleLogout}\n                    className=\"text-gray-700 hover:text-red-600 block px-3 py-2 rounded-md text-base font-medium w-full text-left\"\n                  >\n                    Logout\n                  </button>\n                </>\n              ) : (\n                <>\n                  <Link\n                    href=\"/login\"\n                    className=\"text-gray-700 hover:text-green-600 block px-3 py-2 rounded-md text-base font-medium\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Login\n                  </Link>\n                  <Link\n                    href=\"/register\"\n                    className=\"bg-green-600 hover:bg-green-700 text-white block px-3 py-2 rounded-md text-base font-medium mx-3\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Sign Up\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB;QACA,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;sCAKtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;gCAGA,qBACC;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;;wDAAwB;wDAAU,KAAK,IAAI;;;;;;;8DAC3D,6LAAC;oDACC,SAAS;oDACT,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;;iEAKZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAQP,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAET,2BAAa,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM/D,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;4BAGA,qBACC;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;kDAGD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;;gDAAwB;gDAAU,KAAK,IAAI;;;;;;;;;;;;kDAE7D,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;6DAKH;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GAhJwB;;QACG,kIAAA,CAAA,UAAO;;;KADV", "debugId": null}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/SaaS%20Course/Augment/trekInn/frontend/src/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Mountain, Mail, Phone } from 'lucide-react';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Logo and Description */}\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <Mountain className=\"h-8 w-8 text-green-400\" />\n              <span className=\"text-xl font-bold\">TrekInn</span>\n            </div>\n            <p className=\"text-gray-300 mb-4 max-w-md\">\n              Discover the world&apos;s most breathtaking trekking adventures. From the Himalayas to the Andes,\n              we offer expertly guided treks that create memories to last a lifetime.\n            </p>\n            <div className=\"flex space-x-4\">\n              <div className=\"flex items-center space-x-2 text-gray-300\">\n                <Mail className=\"h-4 w-4\" />\n                <span className=\"text-sm\"><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-gray-300\">\n                <Phone className=\"h-4 w-4\" />\n                <span className=\"text-sm\">+****************</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/\" className=\"text-gray-300 hover:text-green-400 transition-colors\">\n                  Home\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/treks\" className=\"text-gray-300 hover:text-green-400 transition-colors\">\n                  All Treks\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-gray-300 hover:text-green-400 transition-colors\">\n                  About Us\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-300 hover:text-green-400 transition-colors\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Support</h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/help\" className=\"text-gray-300 hover:text-green-400 transition-colors\">\n                  Help Center\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/booking-policy\" className=\"text-gray-300 hover:text-green-400 transition-colors\">\n                  Booking Policy\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/cancellation\" className=\"text-gray-300 hover:text-green-400 transition-colors\">\n                  Cancellation\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/safety\" className=\"text-gray-300 hover:text-green-400 transition-colors\">\n                  Safety Guidelines\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-gray-400 text-sm\">\n            © 2025 TrekInn. All rights reserved.\n          </p>\n          <div className=\"flex space-x-6 mt-4 md:mt-0\">\n            <Link href=\"/privacy\" className=\"text-gray-400 hover:text-green-400 text-sm transition-colors\">\n              Privacy Policy\n            </Link>\n            <Link href=\"/terms\" className=\"text-gray-400 hover:text-green-400 text-sm transition-colors\">\n              Terms of Service\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;;;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAuD;;;;;;;;;;;sDAIlF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAuD;;;;;;;;;;;sDAIvF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAuD;;;;;;;;;;;sDAIvF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAuD;;;;;;;;;;;;;;;;;;;;;;;sCAQ7F,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAuD;;;;;;;;;;;sDAItF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkB,WAAU;0DAAuD;;;;;;;;;;;sDAIhG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAuD;;;;;;;;;;;sDAI9F,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS9F,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAGrC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA+D;;;;;;8CAG/F,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA+D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzG;KAnGwB", "debugId": null}}, {"offset": {"line": 708, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/SaaS%20Course/Augment/trekInn/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Trek } from '@/types';\nimport { trekAPI } from '@/lib/api';\nimport Navbar from '@/components/Navbar';\nimport Footer from '@/components/Footer';\nimport { Calendar, Users, Star, ArrowRight, MapPin } from 'lucide-react';\n\nexport default function Home() {\n  const [featuredTreks, setFeaturedTreks] = useState<Trek[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchFeaturedTreks = async () => {\n      try {\n        const response = await trekAPI.getAll({ per_page: 3, sort_by: 'rating', sort_order: 'desc' });\n        setFeaturedTreks(response.data.data);\n      } catch (error) {\n        console.error('Error fetching featured treks:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchFeaturedTreks();\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Navbar />\n\n      {/* Hero Section */}\n      <section className=\"relative h-screen flex items-center justify-center bg-gradient-to-r from-green-800 to-blue-800\">\n        <div className=\"absolute inset-0 bg-black opacity-40\"></div>\n        <div\n          className=\"absolute inset-0 bg-cover bg-center\"\n          style={{\n            backgroundImage: 'url(\"https://images.unsplash.com/photo-1544735716-392fe2489ffa?w=1920&h=1080&fit=crop\")'\n          }}\n        ></div>\n        <div className=\"relative z-10 text-center text-white max-w-4xl mx-auto px-4\">\n          <h1 className=\"text-5xl md:text-7xl font-bold mb-6\">\n            Adventure Awaits\n          </h1>\n          <p className=\"text-xl md:text-2xl mb-8 max-w-2xl mx-auto\">\n            Discover breathtaking trekking adventures around the world. From the Himalayas to the Andes,\n            create memories that will last a lifetime.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/treks\"\n              className=\"bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-flex items-center justify-center\"\n            >\n              Explore Treks\n              <ArrowRight className=\"ml-2 h-5 w-5\" />\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 rounded-lg text-lg font-semibold transition-colors\"\n            >\n              Learn More\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Treks Section */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Featured Adventures\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Discover our most popular trekking destinations, carefully selected for their\n              breathtaking beauty and unforgettable experiences.\n            </p>\n          </div>\n\n          {loading ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              {[1, 2, 3].map((i) => (\n                <div key={i} className=\"bg-white rounded-lg shadow-lg overflow-hidden animate-pulse\">\n                  <div className=\"h-64 bg-gray-300\"></div>\n                  <div className=\"p-6\">\n                    <div className=\"h-6 bg-gray-300 rounded mb-4\"></div>\n                    <div className=\"h-4 bg-gray-300 rounded mb-2\"></div>\n                    <div className=\"h-4 bg-gray-300 rounded mb-4\"></div>\n                    <div className=\"h-8 bg-gray-300 rounded\"></div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              {featuredTreks.map((trek) => (\n                <div key={trek.id} className=\"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow\">\n                  <div className=\"relative h-64\">\n                    <Image\n                      src={trek.image_url || '/placeholder-trek.jpg'}\n                      alt={trek.title}\n                      fill\n                      className=\"object-cover\"\n                    />\n                    <div className=\"absolute top-4 right-4 bg-white px-2 py-1 rounded-full text-sm font-semibold text-gray-900\">\n                      ${trek.price}\n                    </div>\n                  </div>\n                  <div className=\"p-6\">\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-2\">{trek.title}</h3>\n                    <div className=\"flex items-center text-gray-600 mb-2\">\n                      <MapPin className=\"h-4 w-4 mr-1\" />\n                      <span className=\"text-sm\">{trek.location}</span>\n                    </div>\n                    <p className=\"text-gray-600 mb-4 line-clamp-2\">{trek.description}</p>\n\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                        <div className=\"flex items-center\">\n                          <Calendar className=\"h-4 w-4 mr-1\" />\n                          <span>{trek.duration_days} days</span>\n                        </div>\n                        <div className=\"flex items-center\">\n                          <Users className=\"h-4 w-4 mr-1\" />\n                          <span>{trek.available_spots} spots</span>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Star className=\"h-4 w-4 text-yellow-400 mr-1\" />\n                        <span className=\"text-sm font-semibold\">{trek.rating}</span>\n                      </div>\n                    </div>\n\n                    <Link\n                      href={`/treks/${trek.id}`}\n                      className=\"w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-semibold transition-colors inline-flex items-center justify-center\"\n                    >\n                      View Details\n                      <ArrowRight className=\"ml-2 h-4 w-4\" />\n                    </Link>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n\n          <div className=\"text-center mt-12\">\n            <Link\n              href=\"/treks\"\n              className=\"bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors inline-flex items-center\"\n            >\n              View All Treks\n              <ArrowRight className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"py-16 bg-green-600\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 text-center text-white\">\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">500+</div>\n              <div className=\"text-green-100\">Happy Trekkers</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">50+</div>\n              <div className=\"text-green-100\">Trek Destinations</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">15+</div>\n              <div className=\"text-green-100\">Countries</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">4.8</div>\n              <div className=\"text-green-100\">Average Rating</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;qDAAqB;oBACzB,IAAI;wBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM,CAAC;4BAAE,UAAU;4BAAG,SAAS;4BAAU,YAAY;wBAAO;wBAC3F,iBAAiB,SAAS,IAAI,CAAC,IAAI;oBACrC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAClD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;yBAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;0BAGP,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB;wBACnB;;;;;;kCAEF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAI1D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;4CACX;0DAEC,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;wBAMxD,wBACC,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC;oCAAY,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;mCANT;;;;;;;;;iDAYd,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;oCAAkB,WAAU;;sDAC3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,KAAK,SAAS,IAAI;oDACvB,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;wDAA6F;wDACxG,KAAK,KAAK;;;;;;;;;;;;;sDAGhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwC,KAAK,KAAK;;;;;;8DAChE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAW,KAAK,QAAQ;;;;;;;;;;;;8DAE1C,6LAAC;oDAAE,WAAU;8DAAmC,KAAK,WAAW;;;;;;8DAEhE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,6LAAC;;gFAAM,KAAK,aAAa;gFAAC;;;;;;;;;;;;;8EAE5B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,6LAAC;;gFAAM,KAAK,eAAe;gFAAC;;;;;;;;;;;;;;;;;;;sEAGhC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;8EAAyB,KAAK,MAAM;;;;;;;;;;;;;;;;;;8DAIxD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;oDACzB,WAAU;;wDACX;sEAEC,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;mCA1ClB,KAAK,EAAE;;;;;;;;;;sCAkDvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDAEC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9B,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAiB;;;;;;;;;;;;0CAElC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAiB;;;;;;;;;;;;0CAElC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAiB;;;;;;;;;;;;0CAElC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMxC,6LAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GAjLwB;KAAA", "debugId": null}}]}