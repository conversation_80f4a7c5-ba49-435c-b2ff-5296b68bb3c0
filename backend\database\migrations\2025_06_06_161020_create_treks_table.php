<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('treks', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->text('detailed_description')->nullable();
            $table->string('location');
            $table->integer('duration_days');
            $table->enum('difficulty', ['easy', 'moderate', 'difficult', 'extreme']);
            $table->decimal('price', 10, 2);
            $table->integer('max_participants');
            $table->integer('available_spots');
            $table->date('start_date');
            $table->date('end_date');
            $table->json('included_items')->nullable(); // What's included in the trek
            $table->json('required_items')->nullable(); // What participants need to bring
            $table->string('image_url')->nullable();
            $table->json('gallery_images')->nullable(); // Additional images
            $table->boolean('is_active')->default(true);
            $table->decimal('rating', 3, 2)->default(0); // Average rating
            $table->integer('total_reviews')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('treks');
    }
};
