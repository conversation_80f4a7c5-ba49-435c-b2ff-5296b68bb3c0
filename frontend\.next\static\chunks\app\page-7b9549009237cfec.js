(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2138:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},3792:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var a=t(5155),l=t(2115),r=t(6874),i=t.n(r),d=t(6766),c=t(5731),n=t(5494),x=t(6821),m=t(2138),o=t(4516),h=t(9074),g=t(7580),j=t(8564);function b(){let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0);return(0,l.useEffect)(()=>{(async()=>{try{let e=await c.PR.getAll({per_page:3,sort_by:"rating",sort_order:"desc"});s(e.data.data)}catch(e){console.error("Error fetching featured treks:",e)}finally{r(!1)}})()},[]),(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)(n.A,{}),(0,a.jsxs)("section",{className:"relative h-screen flex items-center justify-center bg-gradient-to-r from-green-800 to-blue-800",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black opacity-40"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-cover bg-center",style:{backgroundImage:'url("https://images.unsplash.com/photo-1544735716-392fe2489ffa?w=1920&h=1080&fit=crop")'}}),(0,a.jsxs)("div",{className:"relative z-10 text-center text-white max-w-4xl mx-auto px-4",children:[(0,a.jsx)("h1",{className:"text-5xl md:text-7xl font-bold mb-6",children:"Adventure Awaits"}),(0,a.jsx)("p",{className:"text-xl md:text-2xl mb-8 max-w-2xl mx-auto",children:"Discover breathtaking trekking adventures around the world. From the Himalayas to the Andes, create memories that will last a lifetime."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsxs)(i(),{href:"/treks",className:"bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-flex items-center justify-center",children:["Explore Treks",(0,a.jsx)(m.A,{className:"ml-2 h-5 w-5"})]}),(0,a.jsx)(i(),{href:"/about",className:"border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 rounded-lg text-lg font-semibold transition-colors",children:"Learn More"})]})]})]}),(0,a.jsx)("section",{className:"py-16 bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Featured Adventures"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Discover our most popular trekking destinations, carefully selected for their breathtaking beauty and unforgettable experiences."})]}),t?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[1,2,3].map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden animate-pulse",children:[(0,a.jsx)("div",{className:"h-64 bg-gray-300"}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-300 rounded mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-4"}),(0,a.jsx)("div",{className:"h-8 bg-gray-300 rounded"})]})]},e))}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:e.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow",children:[(0,a.jsxs)("div",{className:"relative h-64",children:[(0,a.jsx)(d.default,{src:e.image_url||"/placeholder-trek.jpg",alt:e.title,fill:!0,className:"object-cover"}),(0,a.jsxs)("div",{className:"absolute top-4 right-4 bg-white px-2 py-1 rounded-full text-sm font-semibold text-gray-900",children:["$",e.price]})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center text-gray-600 mb-2",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:e.location})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-1"}),(0,a.jsxs)("span",{children:[e.duration_days," days"]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-1"}),(0,a.jsxs)("span",{children:[e.available_spots," spots"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 text-yellow-400 mr-1"}),(0,a.jsx)("span",{className:"text-sm font-semibold",children:e.rating})]})]}),(0,a.jsxs)(i(),{href:"/treks/".concat(e.id),className:"w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-semibold transition-colors inline-flex items-center justify-center",children:["View Details",(0,a.jsx)(m.A,{className:"ml-2 h-4 w-4"})]})]})]},e.id))}),(0,a.jsx)("div",{className:"text-center mt-12",children:(0,a.jsxs)(i(),{href:"/treks",className:"bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors inline-flex items-center",children:["View All Treks",(0,a.jsx)(m.A,{className:"ml-2 h-5 w-5"})]})})]})}),(0,a.jsx)("section",{className:"py-16 bg-green-600",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8 text-center text-white",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-4xl font-bold mb-2",children:"500+"}),(0,a.jsx)("div",{className:"text-green-100",children:"Happy Trekkers"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-4xl font-bold mb-2",children:"50+"}),(0,a.jsx)("div",{className:"text-green-100",children:"Trek Destinations"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-4xl font-bold mb-2",children:"15+"}),(0,a.jsx)("div",{className:"text-green-100",children:"Countries"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-4xl font-bold mb-2",children:"4.8"}),(0,a.jsx)("div",{className:"text-green-100",children:"Average Rating"})]})]})})}),(0,a.jsx)(x.A,{})]})}},7524:(e,s,t)=>{Promise.resolve().then(t.bind(t,3792))}},e=>{var s=s=>e(e.s=s);e.O(0,[464,988,384,202,441,684,358],()=>s(7524)),_N_E=e.O()}]);