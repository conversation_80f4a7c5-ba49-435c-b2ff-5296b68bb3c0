'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { Trek } from '@/types';
import { trekAPI } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { 
  Calendar, 
  Users, 
  Star, 
  MapPin, 
  Clock, 
  DollarSign,
  CheckCircle,
  Package,
  AlertTriangle,
  ArrowLeft
} from 'lucide-react';

export default function TrekDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [trek, setTrek] = useState<Trek | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchTrek = async () => {
      try {
        const response = await trekAPI.getById(params.id as string);
        setTrek(response.data.trek);
      } catch (err) {
        setError('Trek not found');
        console.error('Error fetching trek:', err);
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchTrek();
    }
  }, [params.id]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'moderate': return 'bg-yellow-100 text-yellow-800';
      case 'difficult': return 'bg-orange-100 text-orange-800';
      case 'extreme': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleBookNow = () => {
    if (!user) {
      router.push('/login');
      return;
    }
    router.push(`/booking/${trek?.id}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="animate-pulse">
          <div className="h-96 bg-gray-300"></div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="h-8 bg-gray-300 rounded mb-4"></div>
                <div className="h-4 bg-gray-300 rounded mb-2"></div>
                <div className="h-4 bg-gray-300 rounded mb-4"></div>
                <div className="h-32 bg-gray-300 rounded"></div>
              </div>
              <div className="h-64 bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error || !trek) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Trek Not Found</h1>
          <p className="text-gray-600 mb-8">The trek you&apos;re looking for doesn&apos;t exist or has been removed.</p>
          <Link
            href="/treks"
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold inline-flex items-center"
          >
            <ArrowLeft className="mr-2 h-5 w-5" />
            Back to Treks
          </Link>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      
      {/* Hero Image */}
      <div className="relative h-96">
        <Image
          src={trek.image_url || '/placeholder-trek.jpg'}
          alt={trek.title}
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        <div className="absolute bottom-8 left-8">
          <span className={`px-3 py-1 rounded-full text-sm font-semibold ${getDifficultyColor(trek.difficulty)}`}>
            {trek.difficulty.charAt(0).toUpperCase() + trek.difficulty.slice(1)}
          </span>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-6">
          <Link
            href="/treks"
            className="text-green-600 hover:text-green-700 inline-flex items-center mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to all treks
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">{trek.title}</h1>
            
            <div className="flex items-center text-gray-600 mb-6">
              <MapPin className="h-5 w-5 mr-2" />
              <span className="text-lg">{trek.location}</span>
            </div>

            <div className="flex items-center space-x-6 mb-8">
              <div className="flex items-center">
                <Star className="h-5 w-5 text-yellow-400 mr-1" />
                <span className="font-semibold">{trek.rating}</span>
                <span className="text-gray-600 ml-1">({trek.total_reviews} reviews)</span>
              </div>
              <div className="flex items-center text-gray-600">
                <Calendar className="h-5 w-5 mr-1" />
                <span>{trek.duration_days} days</span>
              </div>
              <div className="flex items-center text-gray-600">
                <Users className="h-5 w-5 mr-1" />
                <span>{trek.available_spots} spots available</span>
              </div>
            </div>

            <div className="prose max-w-none mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">About This Trek</h2>
              <p className="text-gray-700 text-lg leading-relaxed mb-6">
                {trek.description}
              </p>
              {trek.detailed_description && (
                <div className="text-gray-700 leading-relaxed">
                  {trek.detailed_description.split('\n').map((paragraph, index) => (
                    <p key={index} className="mb-4">{paragraph}</p>
                  ))}
                </div>
              )}
            </div>

            {/* What's Included */}
            {trek.included_items && trek.included_items.length > 0 && (
              <div className="mb-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                  <CheckCircle className="h-6 w-6 text-green-600 mr-2" />
                  What&apos;s Included
                </h3>
                <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {trek.included_items.map((item, index) => (
                    <li key={index} className="flex items-center text-gray-700">
                      <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* What to Bring */}
            {trek.required_items && trek.required_items.length > 0 && (
              <div className="mb-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                  <Package className="h-6 w-6 text-blue-600 mr-2" />
                  What to Bring
                </h3>
                <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {trek.required_items.map((item, index) => (
                    <li key={index} className="flex items-center text-gray-700">
                      <Package className="h-4 w-4 text-blue-600 mr-2 flex-shrink-0" />
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* Booking Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-6 sticky top-8">
              <div className="text-center mb-6">
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  ${trek.price}
                  <span className="text-lg font-normal text-gray-600"> / person</span>
                </div>
                <div className="text-sm text-gray-600">
                  {trek.duration_days} day adventure
                </div>
              </div>

              <div className="space-y-4 mb-6">
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <div className="flex items-center text-gray-600">
                    <Clock className="h-4 w-4 mr-2" />
                    <span>Duration</span>
                  </div>
                  <span className="font-semibold">{trek.duration_days} days</span>
                </div>

                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <div className="flex items-center text-gray-600">
                    <Users className="h-4 w-4 mr-2" />
                    <span>Group Size</span>
                  </div>
                  <span className="font-semibold">Max {trek.max_participants}</span>
                </div>

                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <div className="flex items-center text-gray-600">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span>Available</span>
                  </div>
                  <span className="font-semibold">{trek.available_spots} spots</span>
                </div>

                <div className="flex items-center justify-between py-2">
                  <div className="flex items-center text-gray-600">
                    <DollarSign className="h-4 w-4 mr-2" />
                    <span>Difficulty</span>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getDifficultyColor(trek.difficulty)}`}>
                    {trek.difficulty.charAt(0).toUpperCase() + trek.difficulty.slice(1)}
                  </span>
                </div>
              </div>

              {trek.available_spots > 0 ? (
                <button
                  onClick={handleBookNow}
                  className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-semibold text-lg transition-colors"
                >
                  {user ? 'Book Now' : 'Login to Book'}
                </button>
              ) : (
                <button
                  disabled
                  className="w-full bg-gray-400 text-white py-3 px-4 rounded-lg font-semibold text-lg cursor-not-allowed"
                >
                  Fully Booked
                </button>
              )}

              <div className="mt-4 text-center">
                <p className="text-sm text-gray-600">
                  Free cancellation up to 48 hours before departure
                </p>
              </div>

              {trek.available_spots <= 5 && trek.available_spots > 0 && (
                <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                  <div className="flex items-center text-orange-800">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    <span className="text-sm font-medium">
                      Only {trek.available_spots} spot{trek.available_spots !== 1 ? 's' : ''} left!
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
