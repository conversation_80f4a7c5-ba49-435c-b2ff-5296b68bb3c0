(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[550],{731:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j});var a=r(5155),l=r(2115),t=r(6874),d=r.n(t),i=r(6766),c=r(5731),n=r(5494),o=r(6821),x=r(9946);let m=(0,x.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),g=(0,x.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);var u=r(4516),h=r(9074),p=r(7580),b=r(8564),f=r(2138);function j(){let[e,s]=(0,l.useState)(null),[r,t]=(0,l.useState)(!0),[x,j]=(0,l.useState)(""),[y,v]=(0,l.useState)({difficulty:"",min_price:"",max_price:"",max_duration:"",sort_by:"created_at",sort_order:"desc"}),[N,w]=(0,l.useState)(!1),_=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;t(!0);try{let r={page:e,per_page:12,search:x,...y};Object.keys(r).forEach(e=>{""===r[e]&&delete r[e]});let a=await c.PR.getAll(r);s(a.data)}catch(e){console.error("Error fetching treks:",e)}finally{t(!1)}};(0,l.useEffect)(()=>{_()},[x,y]);let k=(e,s)=>{v(r=>({...r,[e]:s}))},A=()=>{v({difficulty:"",min_price:"",max_price:"",max_duration:"",sort_by:"created_at",sort_order:"desc"}),j("")},C=e=>{switch(e){case"easy":return"bg-green-100 text-green-800";case"moderate":return"bg-yellow-100 text-yellow-800";case"difficult":return"bg-orange-100 text-orange-800";case"extreme":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)(n.A,{}),(0,a.jsx)("div",{className:"bg-green-600 text-white py-16",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:"Discover Amazing Treks"}),(0,a.jsx)("p",{className:"text-xl text-green-100 max-w-2xl mx-auto",children:"Explore our collection of carefully curated trekking adventures from around the world"})]})})}),(0,a.jsx)("div",{className:"bg-gray-50 py-8",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("form",{onSubmit:e=>{e.preventDefault(),_()},className:"mb-6",children:(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(m,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,a.jsx)("input",{type:"text",placeholder:"Search treks by name, location, or description...",value:x,onChange:e=>j(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"})]}),(0,a.jsxs)("button",{type:"button",onClick:()=>w(!N),className:"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50",children:[(0,a.jsx)(g,{className:"h-5 w-5"}),"Filters"]})]})}),N&&(0,a.jsxs)("div",{className:"border-t pt-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Difficulty"}),(0,a.jsxs)("select",{value:y.difficulty,onChange:e=>k("difficulty",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",children:[(0,a.jsx)("option",{value:"",children:"All Difficulties"}),(0,a.jsx)("option",{value:"easy",children:"Easy"}),(0,a.jsx)("option",{value:"moderate",children:"Moderate"}),(0,a.jsx)("option",{value:"difficult",children:"Difficult"}),(0,a.jsx)("option",{value:"extreme",children:"Extreme"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Min Price ($)"}),(0,a.jsx)("input",{type:"number",placeholder:"0",value:y.min_price,onChange:e=>k("min_price",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Price ($)"}),(0,a.jsx)("input",{type:"number",placeholder:"10000",value:y.max_price,onChange:e=>k("max_price",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Duration (days)"}),(0,a.jsx)("input",{type:"number",placeholder:"30",value:y.max_duration,onChange:e=>k("max_duration",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sort by"}),(0,a.jsxs)("select",{value:y.sort_by,onChange:e=>k("sort_by",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",children:[(0,a.jsx)("option",{value:"created_at",children:"Newest"}),(0,a.jsx)("option",{value:"price",children:"Price"}),(0,a.jsx)("option",{value:"duration_days",children:"Duration"}),(0,a.jsx)("option",{value:"rating",children:"Rating"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Order"}),(0,a.jsxs)("select",{value:y.sort_order,onChange:e=>k("sort_order",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",children:[(0,a.jsx)("option",{value:"desc",children:"Descending"}),(0,a.jsx)("option",{value:"asc",children:"Ascending"})]})]})]}),(0,a.jsx)("button",{onClick:A,className:"text-green-600 hover:text-green-700 font-medium",children:"Clear all filters"})]})]})]})})}),(0,a.jsx)("div",{className:"py-12",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:r?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[...Array(6)].map((e,s)=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden animate-pulse",children:[(0,a.jsx)("div",{className:"h-64 bg-gray-300"}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-300 rounded mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-4"}),(0,a.jsx)("div",{className:"h-8 bg-gray-300 rounded"})]})]},s))}):e&&e.data.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-gray-900",children:[e.total," Trek",1!==e.total?"s":""," Found"]}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Showing ",e.from,"-",e.to," of ",e.total," results"]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.data.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow",children:[(0,a.jsxs)("div",{className:"relative h-64",children:[(0,a.jsx)(i.default,{src:e.image_url||"/placeholder-trek.jpg",alt:e.title,fill:!0,className:"object-cover"}),(0,a.jsxs)("div",{className:"absolute top-4 right-4 bg-white px-2 py-1 rounded-full text-sm font-semibold text-gray-900",children:["$",e.price]}),(0,a.jsx)("div",{className:"absolute top-4 left-4",children:(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-semibold ".concat(C(e.difficulty)),children:e.difficulty.charAt(0).toUpperCase()+e.difficulty.slice(1)})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center text-gray-600 mb-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:e.location})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-1"}),(0,a.jsxs)("span",{children:[e.duration_days," days"]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),(0,a.jsxs)("span",{children:[e.available_spots," spots"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-yellow-400 mr-1"}),(0,a.jsx)("span",{className:"text-sm font-semibold",children:e.rating})]})]}),(0,a.jsxs)(d(),{href:"/treks/".concat(e.id),className:"w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-semibold transition-colors inline-flex items-center justify-center",children:["View Details",(0,a.jsx)(f.A,{className:"ml-2 h-4 w-4"})]})]})]},e.id))}),e.last_page>1&&(0,a.jsx)("div",{className:"mt-12 flex justify-center",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[e.prev_page_url&&(0,a.jsx)("button",{onClick:()=>_(e.current_page-1),className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:"Previous"}),[...Array(Math.min(5,e.last_page))].map((s,r)=>{let l=r+1;return(0,a.jsx)("button",{onClick:()=>_(l),className:"px-4 py-2 border rounded-md ".concat(l===e.current_page?"bg-green-600 text-white border-green-600":"border-gray-300 hover:bg-gray-50"),children:l},l)}),e.next_page_url&&(0,a.jsx)("button",{onClick:()=>_(e.current_page+1),className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:"Next"})]})})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(m,{className:"h-16 w-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No treks found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Try adjusting your search criteria or filters to find more results."}),(0,a.jsx)("button",{onClick:A,className:"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold",children:"Clear filters"})]})})}),(0,a.jsx)(o.A,{})]})}},2138:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},6795:(e,s,r)=>{Promise.resolve().then(r.bind(r,731))}},e=>{var s=s=>e(e.s=s);e.O(0,[464,988,384,202,441,684,358],()=>s(6795)),_N_E=e.O()}]);