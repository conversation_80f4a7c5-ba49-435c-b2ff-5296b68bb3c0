<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Booking extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'trek_id',
        'booking_reference',
        'participants_count',
        'total_amount',
        'status',
        'booking_date',
        'participant_details',
        'special_requirements',
        'emergency_contact_name',
        'emergency_contact_phone',
        'payment_status',
        'payment_method',
        'payment_reference',
        'payment_date',
    ];

    protected $casts = [
        'participant_details' => 'array',
        'booking_date' => 'date',
        'payment_date' => 'datetime',
        'total_amount' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($booking) {
            if (empty($booking->booking_reference)) {
                $booking->booking_reference = 'TRK-' . strtoupper(Str::random(8));
            }
        });
    }

    /**
     * Get the user that owns the booking.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the trek that the booking belongs to.
     */
    public function trek(): BelongsTo
    {
        return $this->belongsTo(Trek::class);
    }

    /**
     * Check if booking can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'confirmed']) &&
               $this->booking_date->isFuture();
    }

    /**
     * Cancel the booking
     */
    public function cancel(): void
    {
        if ($this->canBeCancelled()) {
            $this->update(['status' => 'cancelled']);
            $this->trek->increaseAvailableSpots($this->participants_count);
        }
    }
}
