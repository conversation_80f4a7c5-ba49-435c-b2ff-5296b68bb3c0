(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{646:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2162:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var t=a(5155),l=a(2115),r=a(6874),c=a.n(r),d=a(283),i=a(5695),n=a(5731),x=a(5494),m=a(6821),o=a(646),h=a(4186);let g=(0,a(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var u=a(5339),p=a(9074),y=a(5868),j=a(4516),N=a(7580),f=a(2657);function b(){let{user:e,loading:s}=(0,d.A)(),a=(0,i.useRouter)(),[r,b]=(0,l.useState)(null),[v,k]=(0,l.useState)(!0);(0,l.useEffect)(()=>{if(!s&&!e)return void a.push("/login");e&&w()},[e,s,a]);let w=async()=>{try{let e=await n.iC.getAll();b(e.data)}catch(e){console.error("Error fetching bookings:",e)}finally{k(!1)}},A=e=>{switch(e){case"confirmed":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"cancelled":return"bg-red-100 text-red-800";case"completed":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},_=e=>{switch(e){case"confirmed":case"completed":return(0,t.jsx)(o.A,{className:"h-4 w-4"});case"pending":return(0,t.jsx)(h.A,{className:"h-4 w-4"});case"cancelled":return(0,t.jsx)(g,{className:"h-4 w-4"});default:return(0,t.jsx)(u.A,{className:"h-4 w-4"})}};return s||v?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(x.A,{}),(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-300 rounded mb-8"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-300 rounded mb-4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-300 rounded"})]},e))}),(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-300 rounded mb-4"}),(0,t.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-300 rounded"})]},e))})]})}),(0,t.jsx)(m.A,{})]}):e?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(x.A,{}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Welcome back, ",e.name,"!"]}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your trekking adventures and bookings"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,t.jsx)(p.A,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Bookings"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==r?void 0:r.total)||0})]})]})}),(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,t.jsx)(o.A,{className:"h-6 w-6 text-blue-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Confirmed Trips"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==r?void 0:r.data.filter(e=>"confirmed"===e.status).length)||0})]})]})}),(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,t.jsx)(y.A,{className:"h-6 w-6 text-purple-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Spent"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["$",(null==r?void 0:r.data.reduce((e,s)=>e+parseFloat(s.total_amount),0).toFixed(2))||"0.00"]})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Your Bookings"})}),r&&r.data.length>0?(0,t.jsx)("div",{className:"divide-y divide-gray-200",children:r.data.map(e=>{var s,a,l;return(0,t.jsx)("div",{className:"p-6 hover:bg-gray-50",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:null==(s=e.trek)?void 0:s.title}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(A(e.status)),children:[_(e.status),(0,t.jsx)("span",{className:"ml-1 capitalize",children:e.status})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-1"}),(0,t.jsx)("span",{children:null==(a=e.trek)?void 0:a.location})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-1"}),(0,t.jsx)("span",{children:new Date(e.booking_date).toLocaleDateString()})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-1"}),(0,t.jsxs)("span",{children:[e.participants_count," participant",1!==e.participants_count?"s":""]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-1"}),(0,t.jsxs)("span",{children:["$",e.total_amount]})]})]}),(0,t.jsx)("div",{className:"mt-3 text-sm text-gray-500",children:(0,t.jsxs)("span",{children:["Booking Reference: ",e.booking_reference]})})]}),(0,t.jsxs)("div",{className:"ml-6 flex items-center space-x-3",children:[(0,t.jsxs)(c(),{href:"/treks/".concat(null==(l=e.trek)?void 0:l.id),className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-1"}),"View Trek"]}),"pending"===e.status&&(0,t.jsx)("button",{className:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:"Cancel"})]})]})},e.id)})}):(0,t.jsxs)("div",{className:"p-12 text-center",children:[(0,t.jsx)(p.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No bookings yet"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Start your adventure by booking your first trek!"}),(0,t.jsx)(c(),{href:"/treks",className:"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold inline-flex items-center",children:"Browse Treks"})]})]}),(0,t.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(c(),{href:"/treks",className:"block w-full text-left px-4 py-2 text-green-600 hover:bg-green-50 rounded-md transition-colors",children:"Browse New Treks"}),(0,t.jsx)(c(),{href:"/profile",className:"block w-full text-left px-4 py-2 text-green-600 hover:bg-green-50 rounded-md transition-colors",children:"Update Profile"}),(0,t.jsx)(c(),{href:"/help",className:"block w-full text-left px-4 py-2 text-green-600 hover:bg-green-50 rounded-md transition-colors",children:"Get Help"})]})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Account Info"}),(0,t.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Email:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:e.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Member since:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:new Date(e.created_at).toLocaleDateString()})]})]})]})]})]}),(0,t.jsx)(m.A,{})]}):null}},2657:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4186:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4516:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4652:(e,s,a)=>{Promise.resolve().then(a.bind(a,2162))},5339:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5695:(e,s,a)=>{"use strict";var t=a(8999);a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}})},5868:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},7580:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9074:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[464,988,202,441,684,358],()=>s(4652)),_N_E=e.O()}]);