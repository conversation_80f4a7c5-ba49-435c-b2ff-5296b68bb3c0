(()=>{var e={};e.id=484,e.ids=[484],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1403:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(5239),a=t(8088),l=t(8170),i=t.n(l),n=t(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["treks",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8312)),"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\treks\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\treks\\[id]\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/treks/[id]/page",pathname:"/treks/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1630:e=>{"use strict";e.exports=require("http")},1820:e=>{"use strict";e.exports=require("os")},2411:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var r=t(687),a=t(3210),l=t(6189),i=t(474),n=t(5814),d=t.n(n);t(2185);var c=t(3213),o=t(9190),x=t(1317),m=t(8559),p=t(7992),u=t(4398),h=t(228),g=t(1312),b=t(5336),j=t(2688);let f=(0,j.A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]);var y=t(8730),v=t(3928);let N=(0,j.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);function w(){(0,l.useParams)();let e=(0,l.useRouter)(),{user:s}=(0,c.A)(),[t,n]=(0,a.useState)(null),[j,w]=(0,a.useState)(!0),[k,A]=(0,a.useState)(""),_=e=>{switch(e){case"easy":return"bg-green-100 text-green-800";case"moderate":return"bg-yellow-100 text-yellow-800";case"difficult":return"bg-orange-100 text-orange-800";case"extreme":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return j?(0,r.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,r.jsx)(o.A,{}),(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-96 bg-gray-300"}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-300 rounded mb-4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-4"}),(0,r.jsx)("div",{className:"h-32 bg-gray-300 rounded"})]}),(0,r.jsx)("div",{className:"h-64 bg-gray-300 rounded"})]})})]}),(0,r.jsx)(x.A,{})]}):k||!t?(0,r.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,r.jsx)(o.A,{}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Trek Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"The trek you're looking for doesn't exist or has been removed."}),(0,r.jsxs)(d(),{href:"/treks",className:"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold inline-flex items-center",children:[(0,r.jsx)(m.A,{className:"mr-2 h-5 w-5"}),"Back to Treks"]})]}),(0,r.jsx)(x.A,{})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,r.jsx)(o.A,{}),(0,r.jsxs)("div",{className:"relative h-96",children:[(0,r.jsx)(i.default,{src:t.image_url||"/placeholder-trek.jpg",alt:t.title,fill:!0,className:"object-cover"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-40"}),(0,r.jsx)("div",{className:"absolute bottom-8 left-8",children:(0,r.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-semibold ${_(t.difficulty)}`,children:t.difficulty.charAt(0).toUpperCase()+t.difficulty.slice(1)})})]}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)(d(),{href:"/treks",className:"text-green-600 hover:text-green-700 inline-flex items-center mb-4",children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Back to all treks"]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:t.title}),(0,r.jsxs)("div",{className:"flex items-center text-gray-600 mb-6",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 mr-2"}),(0,r.jsx)("span",{className:"text-lg",children:t.location})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-yellow-400 mr-1"}),(0,r.jsx)("span",{className:"font-semibold",children:t.rating}),(0,r.jsxs)("span",{className:"text-gray-600 ml-1",children:["(",t.total_reviews," reviews)"]})]}),(0,r.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 mr-1"}),(0,r.jsxs)("span",{children:[t.duration_days," days"]})]}),(0,r.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,r.jsx)(g.A,{className:"h-5 w-5 mr-1"}),(0,r.jsxs)("span",{children:[t.available_spots," spots available"]})]})]}),(0,r.jsxs)("div",{className:"prose max-w-none mb-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"About This Trek"}),(0,r.jsx)("p",{className:"text-gray-700 text-lg leading-relaxed mb-6",children:t.description}),t.detailed_description&&(0,r.jsx)("div",{className:"text-gray-700 leading-relaxed",children:t.detailed_description.split("\n").map((e,s)=>(0,r.jsx)("p",{className:"mb-4",children:e},s))})]}),t.included_items&&t.included_items.length>0&&(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(b.A,{className:"h-6 w-6 text-green-600 mr-2"}),"What's Included"]}),(0,r.jsx)("ul",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:t.included_items.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-center text-gray-700",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 text-green-600 mr-2 flex-shrink-0"}),e]},s))})]}),t.required_items&&t.required_items.length>0&&(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(f,{className:"h-6 w-6 text-blue-600 mr-2"}),"What to Bring"]}),(0,r.jsx)("ul",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:t.required_items.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-center text-gray-700",children:[(0,r.jsx)(f,{className:"h-4 w-4 text-blue-600 mr-2 flex-shrink-0"}),e]},s))})]})]}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg shadow-lg p-6 sticky top-8",children:[(0,r.jsxs)("div",{className:"text-center mb-6",children:[(0,r.jsxs)("div",{className:"text-3xl font-bold text-gray-900 mb-2",children:["$",t.price,(0,r.jsx)("span",{className:"text-lg font-normal text-gray-600",children:" / person"})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[t.duration_days," day adventure"]})]}),(0,r.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between py-2 border-b border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-2"}),(0,r.jsx)("span",{children:"Duration"})]}),(0,r.jsxs)("span",{className:"font-semibold",children:[t.duration_days," days"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between py-2 border-b border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),(0,r.jsx)("span",{children:"Group Size"})]}),(0,r.jsxs)("span",{className:"font-semibold",children:["Max ",t.max_participants]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between py-2 border-b border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),(0,r.jsx)("span",{children:"Available"})]}),(0,r.jsxs)("span",{className:"font-semibold",children:[t.available_spots," spots"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,r.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),(0,r.jsx)("span",{children:"Difficulty"})]}),(0,r.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${_(t.difficulty)}`,children:t.difficulty.charAt(0).toUpperCase()+t.difficulty.slice(1)})]})]}),t.available_spots>0?(0,r.jsx)("button",{onClick:()=>{if(!s)return void e.push("/login");e.push(`/booking/${t?.id}`)},className:"w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-semibold text-lg transition-colors",children:s?"Book Now":"Login to Book"}):(0,r.jsx)("button",{disabled:!0,className:"w-full bg-gray-400 text-white py-3 px-4 rounded-lg font-semibold text-lg cursor-not-allowed",children:"Fully Booked"}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Free cancellation up to 48 hours before departure"})}),t.available_spots<=5&&t.available_spots>0&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center text-orange-800",children:[(0,r.jsx)(N,{className:"h-4 w-4 mr-2"}),(0,r.jsxs)("span",{className:"text-sm font-medium",children:["Only ",t.available_spots," spot",1!==t.available_spots?"s":""," left!"]})]})})]})})]})]}),(0,r.jsx)(x.A,{})]})}},2412:e=>{"use strict";e.exports=require("assert")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3928:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},3997:e=>{"use strict";e.exports=require("tty")},4e3:(e,s,t)=>{Promise.resolve().then(t.bind(t,2411))},4075:e=>{"use strict";e.exports=require("zlib")},4735:e=>{"use strict";e.exports=require("events")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5856:(e,s,t)=>{Promise.resolve().then(t.bind(t,8312))},6189:(e,s,t)=>{"use strict";var r=t(5773);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}})},7910:e=>{"use strict";e.exports=require("stream")},8312:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\SaaS Course\\\\Augment\\\\trekInn\\\\frontend\\\\src\\\\app\\\\treks\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\treks\\[id]\\page.tsx","default")},8354:e=>{"use strict";e.exports=require("util")},8559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,60,83,680,73],()=>t(1403));module.exports=r})();