(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{2657:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5695:(e,s,r)=>{"use strict";var t=r(8999);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}})},8749:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},9690:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>h});var t=r(5155),a=r(2115),n=r(5695),l=r(6874),d=r.n(l),o=r(283),c=r(5494),i=r(6821),m=r(8491),u=r(8749),x=r(2657);function h(){let[e,s]=(0,a.useState)(""),[r,l]=(0,a.useState)(""),[h,g]=(0,a.useState)(!1),[p,f]=(0,a.useState)(!1),[y,b]=(0,a.useState)(""),{login:j}=(0,o.A)(),v=(0,n.useRouter)(),N=async s=>{s.preventDefault(),f(!0),b("");try{await j(e,r),v.push("/dashboard")}catch(e){e instanceof Error?b(e.message):b("Login failed. Please try again.")}finally{f(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(c.A,{}),(0,t.jsx)("div",{className:"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)(m.A,{className:"h-12 w-12 text-green-600"})}),(0,t.jsx)("h2",{className:"mt-6 text-3xl font-bold text-gray-900",children:"Welcome back to TrekInn"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Sign in to your account to continue your adventure"})]}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:N,children:[y&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md",children:y}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>s(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"Enter your email"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,t.jsxs)("div",{className:"mt-1 relative",children:[(0,t.jsx)("input",{id:"password",name:"password",type:h?"text":"password",autoComplete:"current-password",required:!0,value:r,onChange:e=>l(e.target.value),className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 pr-10",placeholder:"Enter your password"}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>g(!h),children:h?(0,t.jsx)(u.A,{className:"h-5 w-5 text-gray-400"}):(0,t.jsx)(x.A,{className:"h-5 w-5 text-gray-400"})})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"}),(0,t.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),(0,t.jsx)("div",{className:"text-sm",children:(0,t.jsx)(d(),{href:"/forgot-password",className:"font-medium text-green-600 hover:text-green-500",children:"Forgot your password?"})})]}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",disabled:p,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed",children:p?"Signing in...":"Sign in"})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,t.jsx)(d(),{href:"/register",className:"font-medium text-green-600 hover:text-green-500",children:"Sign up for free"})]})})]})]})}),(0,t.jsx)(i.A,{})]})}},9869:(e,s,r)=>{Promise.resolve().then(r.bind(r,9690))}},e=>{var s=s=>e(e.s=s);e.O(0,[464,988,202,441,684,358],()=>s(9869)),_N_E=e.O()}]);