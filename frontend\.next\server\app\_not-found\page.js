(()=>{var e={};e.id=492,e.ids=[492],e.modules={189:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},557:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>l});var o=r(5239),s=r(8088),n=r(8170),i=r.n(n),a=r(893),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);r.d(t,u);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=[],c={require:r,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1630:e=>{"use strict";e.exports=require("http")},1820:e=>{"use strict";e.exports=require("os")},2185:(e,t,r)=>{"use strict";r.d(t,{PR:()=>n,R2:()=>s,iC:()=>i});let o=r(1060).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json",Accept:"application/json"}});o.interceptors.request.use(e=>{let t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),window.location.href="/login"),Promise.reject(e)));let s={register:e=>o.post("/auth/register",e),login:e=>o.post("/auth/login",e),logout:()=>o.post("/auth/logout"),getUser:()=>o.get("/auth/user")},n={getAll:e=>o.get("/treks",{params:e}),getById:e=>o.get(`/treks/${e}`),create:e=>o.post("/treks",e),update:(e,t)=>o.put(`/treks/${e}`,t),delete:e=>o.delete(`/treks/${e}`)},i={getAll:e=>o.get("/bookings",{params:e}),getById:e=>o.get(`/bookings/${e}`),create:e=>o.post("/bookings",e),update:(e,t)=>o.put(`/bookings/${e}`,t),cancel:e=>o.delete(`/bookings/${e}`)}},2412:e=>{"use strict";e.exports=require("assert")},2748:(e,t,r)=>{Promise.resolve().then(r.bind(r,3213))},2925:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>u,AuthProvider:()=>a});var o=r(687),s=r(3210),n=r(2185);let i=(0,s.createContext)(void 0);function a({children:e}){let[t,r]=(0,s.useState)(null),[a,u]=(0,s.useState)(null),[l,d]=(0,s.useState)(!0),c=async(e,t)=>{try{let o=(await n.R2.login({email:e,password:t})).data;r(o.user),u(o.token),localStorage.setItem("auth_token",o.token),localStorage.setItem("user",JSON.stringify(o.user))}catch(e){throw e}},p=async(e,t,o,s)=>{try{let i=(await n.R2.register({name:e,email:t,password:o,password_confirmation:s})).data;r(i.user),u(i.token),localStorage.setItem("auth_token",i.token),localStorage.setItem("user",JSON.stringify(i.user))}catch(e){throw e}},h=async()=>{try{a&&await n.R2.logout()}catch(e){console.error("Logout error:",e)}finally{r(null),u(null),localStorage.removeItem("auth_token"),localStorage.removeItem("user")}};return(0,o.jsx)(i.Provider,{value:{user:t,token:a,login:c,register:p,logout:h,loading:l},children:e})}function u(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var o=r(7413),s=r(2376),n=r.n(s),i=r(8726),a=r.n(i);r(1135);var u=r(9131);let l={title:"TrekInn - Adventure Awaits",description:"Discover amazing trekking adventures around the world with TrekInn"};function d({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsx)("body",{className:`${n().variable} ${a().variable} antialiased`,children:(0,o.jsx)(u.AuthProvider,{children:e})})})}},4604:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var o=r(2907);let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\contexts\\AuthContext.tsx","useAuth")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,60],()=>r(557));module.exports=o})();