(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{2657:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5695:(e,r,s)=>{"use strict";var a=s(8999);s.o(a,"useParams")&&s.d(r,{useParams:function(){return a.useParams}}),s.o(a,"useRouter")&&s.d(r,{useRouter:function(){return a.useRouter}})},6616:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>h});var a=s(5155),t=s(2115),n=s(5695),o=s(6874),l=s.n(o),d=s(283),i=s(5494),c=s(6821),m=s(8491),u=s(8749),x=s(2657);function h(){let[e,r]=(0,t.useState)({name:"",email:"",password:"",passwordConfirmation:""}),[s,o]=(0,t.useState)(!1),[h,p]=(0,t.useState)(!1),[g,f]=(0,t.useState)(!1),[y,b]=(0,t.useState)(""),{register:w}=(0,d.A)(),j=(0,n.useRouter)(),N=s=>{r({...e,[s.target.name]:s.target.value})},v=async r=>{if(r.preventDefault(),f(!0),b(""),e.password!==e.passwordConfirmation){b("Passwords do not match"),f(!1);return}try{await w(e.name,e.email,e.password,e.passwordConfirmation),j.push("/dashboard")}catch(e){e instanceof Error?b(e.message):b("Registration failed. Please try again.")}finally{f(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(i.A,{}),(0,a.jsx)("div",{className:"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(m.A,{className:"h-12 w-12 text-green-600"})}),(0,a.jsx)("h2",{className:"mt-6 text-3xl font-bold text-gray-900",children:"Join TrekInn"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Create your account and start your adventure today"})]}),(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:v,children:[y&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md",children:y}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,a.jsx)("input",{id:"name",name:"name",type:"text",autoComplete:"name",required:!0,value:e.name,onChange:N,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"Enter your full name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:N,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"Enter your email"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,a.jsxs)("div",{className:"mt-1 relative",children:[(0,a.jsx)("input",{id:"password",name:"password",type:s?"text":"password",autoComplete:"new-password",required:!0,value:e.password,onChange:N,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 pr-10",placeholder:"Create a password"}),(0,a.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>o(!s),children:s?(0,a.jsx)(u.A,{className:"h-5 w-5 text-gray-400"}):(0,a.jsx)(x.A,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"passwordConfirmation",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,a.jsxs)("div",{className:"mt-1 relative",children:[(0,a.jsx)("input",{id:"passwordConfirmation",name:"passwordConfirmation",type:h?"text":"password",autoComplete:"new-password",required:!0,value:e.passwordConfirmation,onChange:N,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 pr-10",placeholder:"Confirm your password"}),(0,a.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>p(!h),children:h?(0,a.jsx)(u.A,{className:"h-5 w-5 text-gray-400"}):(0,a.jsx)(x.A,{className:"h-5 w-5 text-gray-400"})})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"terms",name:"terms",type:"checkbox",required:!0,className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"}),(0,a.jsxs)("label",{htmlFor:"terms",className:"ml-2 block text-sm text-gray-900",children:["I agree to the"," ",(0,a.jsx)(l(),{href:"/terms",className:"text-green-600 hover:text-green-500",children:"Terms of Service"})," ","and"," ",(0,a.jsx)(l(),{href:"/privacy",className:"text-green-600 hover:text-green-500",children:"Privacy Policy"})]})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:g,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed",children:g?"Creating account...":"Create account"})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,a.jsx)(l(),{href:"/login",className:"font-medium text-green-600 hover:text-green-500",children:"Sign in here"})]})})]})]})}),(0,a.jsx)(c.A,{})]})}},7051:(e,r,s)=>{Promise.resolve().then(s.bind(s,6616))},8749:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[464,988,202,441,684,358],()=>r(7051)),_N_E=e.O()}]);