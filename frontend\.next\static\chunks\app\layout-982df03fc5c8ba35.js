(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{283:(e,t,o)=>{"use strict";o.d(t,{A:()=>c,AuthProvider:()=>n});var a=o(5155),r=o(2115),s=o(5731);let l=(0,r.createContext)(void 0);function n(e){let{children:t}=e,[o,n]=(0,r.useState)(null),[c,i]=(0,r.useState)(null),[u,g]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=localStorage.getItem("auth_token"),t=localStorage.getItem("user");e&&t&&(i(e),n(JSON.parse(t))),g(!1)},[]);let h=async(e,t)=>{try{let o=(await s.R2.login({email:e,password:t})).data;n(o.user),i(o.token),localStorage.setItem("auth_token",o.token),localStorage.setItem("user",JSON.stringify(o.user))}catch(e){throw e}},m=async(e,t,o,a)=>{try{let r=(await s.R2.register({name:e,email:t,password:o,password_confirmation:a})).data;n(r.user),i(r.token),localStorage.setItem("auth_token",r.token),localStorage.setItem("user",JSON.stringify(r.user))}catch(e){throw e}},d=async()=>{try{c&&await s.R2.logout()}catch(e){console.error("Logout error:",e)}finally{n(null),i(null),localStorage.removeItem("auth_token"),localStorage.removeItem("user")}};return(0,a.jsx)(l.Provider,{value:{user:o,token:c,login:h,register:m,logout:d,loading:u},children:t})}function c(){let e=(0,r.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},347:()=>{},2093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},5731:(e,t,o)=>{"use strict";o.d(t,{PR:()=>s,R2:()=>r,iC:()=>l});let a=o(3464).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json",Accept:"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),window.location.href="/login"),Promise.reject(e)});let r={register:e=>a.post("/auth/register",e),login:e=>a.post("/auth/login",e),logout:()=>a.post("/auth/logout"),getUser:()=>a.get("/auth/user")},s={getAll:e=>a.get("/treks",{params:e}),getById:e=>a.get("/treks/".concat(e)),create:e=>a.post("/treks",e),update:(e,t)=>a.put("/treks/".concat(e),t),delete:e=>a.delete("/treks/".concat(e))},l={getAll:e=>a.get("/bookings",{params:e}),getById:e=>a.get("/bookings/".concat(e)),create:e=>a.post("/bookings",e),update:(e,t)=>a.put("/bookings/".concat(e),t),cancel:e=>a.delete("/bookings/".concat(e))}},7735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},8292:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,2093,23)),Promise.resolve().then(o.t.bind(o,7735,23)),Promise.resolve().then(o.t.bind(o,347,23)),Promise.resolve().then(o.bind(o,283))}},e=>{var t=t=>e(e.s=t);e.O(0,[360,464,441,684,358],()=>t(8292)),_N_E=e.O()}]);