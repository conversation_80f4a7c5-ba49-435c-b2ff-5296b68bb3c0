(()=>{var e={};e.id=130,e.ids=[130],e.modules={228:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1312:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},1630:e=>{"use strict";e.exports=require("http")},1674:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\SaaS Course\\\\Augment\\\\trekInn\\\\frontend\\\\src\\\\app\\\\booking\\\\[trekId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\booking\\[trekId]\\page.tsx","default")},1820:e=>{"use strict";e.exports=require("os")},2412:e=>{"use strict";e.exports=require("assert")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3613:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4067:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(687),a=t(3210),n=t(6189),i=t(2185),l=t(3213),d=t(9190),o=t(1317),c=t(8559),m=t(3613),x=t(228),p=t(1312);let u=(0,t(2688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);function g(){let e=(0,n.useParams)(),r=(0,n.useRouter)(),{user:t}=(0,l.A)(),[g,h]=(0,a.useState)(null),[b,y]=(0,a.useState)(!0),[f,j]=(0,a.useState)(!1),[v,N]=(0,a.useState)(""),[k,_]=(0,a.useState)({participants_count:1,booking_date:"",special_requirements:"",emergency_contact_name:"",emergency_contact_phone:"",participant_details:[{name:"",age:"",dietary_requirements:""}]}),w=e=>{let{name:r,value:t}=e.target;_(e=>({...e,[r]:t}))},q=e=>{let r=Array.from({length:e},(e,r)=>k.participant_details[r]||{name:"",age:"",dietary_requirements:""});_(t=>({...t,participants_count:e,participant_details:r}))},A=(e,r,t)=>{let s=[...k.participant_details];s[e]={...s[e],[r]:t},_(e=>({...e,participant_details:s}))},P=async t=>{t.preventDefault(),j(!0),N("");try{let t={trek_id:e.trekId,participants_count:k.participants_count,booking_date:k.booking_date,participant_details:k.participant_details,special_requirements:k.special_requirements,emergency_contact_name:k.emergency_contact_name,emergency_contact_phone:k.emergency_contact_phone};await i.iC.create(t),r.push("/dashboard?booking=success")}catch(e){e instanceof Error?N(e.message):N("Booking failed. Please try again.")}finally{j(!1)}};if(b)return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(d.A,{}),(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-300 rounded mb-8"}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("div",{className:"h-6 bg-gray-300 rounded mb-4"}),(0,s.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,s.jsx)("div",{className:"h-12 bg-gray-300 rounded"},e))})]})]})}),(0,s.jsx)(o.A,{})]});if(v||!g)return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(d.A,{}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Booking Error"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8",children:v||"Something went wrong"}),(0,s.jsxs)("button",{onClick:()=>r.back(),className:"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold inline-flex items-center",children:[(0,s.jsx)(c.A,{className:"mr-2 h-5 w-5"}),"Go Back"]})]}),(0,s.jsx)(o.A,{})]});let C=parseFloat(g.price)*k.participants_count;return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(d.A,{}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("button",{onClick:()=>r.back(),className:"text-green-600 hover:text-green-700 inline-flex items-center mb-4",children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Back to trek details"]}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Book Your Adventure"}),(0,s.jsxs)("p",{className:"text-gray-600 mt-2",children:["Complete your booking for ",g.title]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)("form",{onSubmit:P,className:"space-y-6",children:[v&&(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md flex items-center",children:[(0,s.jsx)(m.A,{className:"h-5 w-5 mr-2"}),v]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Trip Details"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Number of Participants"}),(0,s.jsx)("select",{value:k.participants_count,onChange:e=>q(parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",required:!0,children:[...Array(Math.min(g.available_spots,8))].map((e,r)=>(0,s.jsxs)("option",{value:r+1,children:[r+1," participant",0!==r?"s":""]},r+1))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Preferred Start Date"}),(0,s.jsx)("input",{type:"date",name:"booking_date",value:k.booking_date,onChange:w,min:g.start_date,max:g.end_date,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",required:!0})]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Participant Information"}),k.participant_details.map((e,r)=>(0,s.jsxs)("div",{className:"mb-6 p-4 border border-gray-200 rounded-lg",children:[(0,s.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3",children:["Participant ",r+1]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name"}),(0,s.jsx)("input",{type:"text",value:e.name,onChange:e=>A(r,"name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Age"}),(0,s.jsx)("input",{type:"number",min:"16",max:"80",value:e.age,onChange:e=>A(r,"age",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Dietary Requirements"}),(0,s.jsx)("input",{type:"text",value:e.dietary_requirements,onChange:e=>A(r,"dietary_requirements",e.target.value),placeholder:"e.g., Vegetarian, Allergies",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"})]})]})]},r))]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Emergency Contact"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Contact Name"}),(0,s.jsx)("input",{type:"text",name:"emergency_contact_name",value:k.emergency_contact_name,onChange:w,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Contact Phone"}),(0,s.jsx)("input",{type:"tel",name:"emergency_contact_phone",value:k.emergency_contact_phone,onChange:w,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",required:!0})]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Special Requirements"}),(0,s.jsx)("textarea",{name:"special_requirements",value:k.special_requirements,onChange:w,rows:4,placeholder:"Any special requirements, medical conditions, or requests...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"})]}),(0,s.jsx)("button",{type:"submit",disabled:f,className:"w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-semibold text-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:f?"Processing Booking...":`Book Now - $${C.toFixed(2)}`})]})}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 sticky top-8",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Booking Summary"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900",children:g.title}),(0,s.jsx)("p",{className:"text-gray-600",children:g.location})]}),(0,s.jsxs)("div",{className:"border-t pt-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Price per person:"}),(0,s.jsxs)("span",{className:"font-semibold",children:["$",g.price]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Participants:"}),(0,s.jsx)("span",{className:"font-semibold",children:k.participants_count})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center text-lg font-bold border-t pt-2",children:[(0,s.jsx)("span",{children:"Total:"}),(0,s.jsxs)("span",{children:["$",C.toFixed(2)]})]})]}),(0,s.jsxs)("div",{className:"border-t pt-4 text-sm text-gray-600",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),(0,s.jsxs)("span",{children:["Duration: ",g.duration_days," days"]})]}),(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),(0,s.jsxs)("span",{children:["Max group: ",g.max_participants]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u,{className:"h-4 w-4 mr-2"}),(0,s.jsx)("span",{children:"Free cancellation up to 48h"})]})]})]})]})})]})]}),(0,s.jsx)(o.A,{})]})}},4075:e=>{"use strict";e.exports=require("zlib")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6189:(e,r,t)=>{"use strict";var s=t(5773);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},8559:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8779:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var s=t(5239),a=t(8088),n=t(8170),i=t.n(n),l=t(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(r,d);let o={children:["",{children:["booking",{children:["[trekId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1674)),"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\booking\\[trekId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\booking\\[trekId]\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/booking/[trekId]/page",pathname:"/booking/[trekId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9214:(e,r,t)=>{Promise.resolve().then(t.bind(t,4067))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9478:(e,r,t)=>{Promise.resolve().then(t.bind(t,1674))},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,60,83,73],()=>t(8779));module.exports=s})();