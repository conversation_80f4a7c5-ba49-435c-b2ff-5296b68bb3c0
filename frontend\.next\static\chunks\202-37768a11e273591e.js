"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[202],{283:(e,t,s)=>{s.d(t,{A:()=>c,AuthProvider:()=>o});var r=s(5155),a=s(2115),l=s(5731);let n=(0,a.createContext)(void 0);function o(e){let{children:t}=e,[s,o]=(0,a.useState)(null),[c,i]=(0,a.useState)(null),[x,d]=(0,a.useState)(!0);(0,a.useEffect)(()=>{let e=localStorage.getItem("auth_token"),t=localStorage.getItem("user");e&&t&&(i(e),o(JSON.parse(t))),d(!1)},[]);let m=async(e,t)=>{try{let s=(await l.R2.login({email:e,password:t})).data;o(s.user),i(s.token),localStorage.setItem("auth_token",s.token),localStorage.setItem("user",JSON.stringify(s.user))}catch(e){throw e}},h=async(e,t,s,r)=>{try{let a=(await l.R2.register({name:e,email:t,password:s,password_confirmation:r})).data;o(a.user),i(a.token),localStorage.setItem("auth_token",a.token),localStorage.setItem("user",JSON.stringify(a.user))}catch(e){throw e}},g=async()=>{try{c&&await l.R2.logout()}catch(e){console.error("Logout error:",e)}finally{o(null),i(null),localStorage.removeItem("auth_token"),localStorage.removeItem("user")}};return(0,r.jsx)(n.Provider,{value:{user:s,token:c,login:m,register:h,logout:g,loading:x},children:t})}function c(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},5494:(e,t,s)=>{s.d(t,{A:()=>m});var r=s(5155),a=s(6874),l=s.n(a),n=s(283),o=s(8491),c=s(4835),i=s(4416),x=s(4783),d=s(2115);function m(){let{user:e,logout:t}=(0,n.A)(),[s,a]=(0,d.useState)(!1),m=()=>{t(),a(!1)};return(0,r.jsx)("nav",{className:"bg-white shadow-lg sticky top-0 z-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(l(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)(o.A,{className:"h-8 w-8 text-green-600"}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"TrekInn"})]})}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[(0,r.jsx)(l(),{href:"/",className:"text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Home"}),(0,r.jsx)(l(),{href:"/treks",className:"text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Treks"}),e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l(),{href:"/dashboard",className:"text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Dashboard"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-gray-700 text-sm",children:["Welcome, ",e.name]}),(0,r.jsxs)("button",{onClick:m,className:"flex items-center space-x-1 text-gray-700 hover:text-red-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Logout"})]})]})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(l(),{href:"/login",className:"text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Login"}),(0,r.jsx)(l(),{href:"/register",className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",children:"Sign Up"})]})]}),(0,r.jsx)("div",{className:"md:hidden flex items-center",children:(0,r.jsx)("button",{onClick:()=>a(!s),className:"text-gray-700 hover:text-green-600 p-2",children:s?(0,r.jsx)(i.A,{className:"h-6 w-6"}):(0,r.jsx)(x.A,{className:"h-6 w-6"})})})]}),s&&(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t",children:[(0,r.jsx)(l(),{href:"/",className:"text-gray-700 hover:text-green-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>a(!1),children:"Home"}),(0,r.jsx)(l(),{href:"/treks",className:"text-gray-700 hover:text-green-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>a(!1),children:"Treks"}),e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l(),{href:"/dashboard",className:"text-gray-700 hover:text-green-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>a(!1),children:"Dashboard"}),(0,r.jsx)("div",{className:"px-3 py-2",children:(0,r.jsxs)("span",{className:"text-gray-700 text-sm",children:["Welcome, ",e.name]})}),(0,r.jsx)("button",{onClick:m,className:"text-gray-700 hover:text-red-600 block px-3 py-2 rounded-md text-base font-medium w-full text-left",children:"Logout"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l(),{href:"/login",className:"text-gray-700 hover:text-green-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>a(!1),children:"Login"}),(0,r.jsx)(l(),{href:"/register",className:"bg-green-600 hover:bg-green-700 text-white block px-3 py-2 rounded-md text-base font-medium mx-3",onClick:()=>a(!1),children:"Sign Up"})]})]})})]})})}},5731:(e,t,s)=>{s.d(t,{PR:()=>l,R2:()=>a,iC:()=>n});let r=s(3464).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json",Accept:"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),window.location.href="/login"),Promise.reject(e)});let a={register:e=>r.post("/auth/register",e),login:e=>r.post("/auth/login",e),logout:()=>r.post("/auth/logout"),getUser:()=>r.get("/auth/user")},l={getAll:e=>r.get("/treks",{params:e}),getById:e=>r.get("/treks/".concat(e)),create:e=>r.post("/treks",e),update:(e,t)=>r.put("/treks/".concat(e),t),delete:e=>r.delete("/treks/".concat(e))},n={getAll:e=>r.get("/bookings",{params:e}),getById:e=>r.get("/bookings/".concat(e)),create:e=>r.post("/bookings",e),update:(e,t)=>r.put("/bookings/".concat(e),t),cancel:e=>r.delete("/bookings/".concat(e))}},6821:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5155),a=s(6874),l=s.n(a),n=s(8491),o=s(1264),c=s(9420);function i(){return(0,r.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)(n.A,{className:"h-8 w-8 text-green-400"}),(0,r.jsx)("span",{className:"text-xl font-bold",children:"TrekInn"})]}),(0,r.jsx)("p",{className:"text-gray-300 mb-4 max-w-md",children:"Discover the world's most breathtaking trekking adventures. From the Himalayas to the Andes, we offer expertly guided treks that create memories to last a lifetime."}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-gray-300",children:[(0,r.jsx)(o.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"text-sm",children:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-gray-300",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"text-sm",children:"+1 (555) 123-4567"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Quick Links"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/",className:"text-gray-300 hover:text-green-400 transition-colors",children:"Home"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/treks",className:"text-gray-300 hover:text-green-400 transition-colors",children:"All Treks"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/about",className:"text-gray-300 hover:text-green-400 transition-colors",children:"About Us"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/contact",className:"text-gray-300 hover:text-green-400 transition-colors",children:"Contact"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Support"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/help",className:"text-gray-300 hover:text-green-400 transition-colors",children:"Help Center"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/booking-policy",className:"text-gray-300 hover:text-green-400 transition-colors",children:"Booking Policy"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/cancellation",className:"text-gray-300 hover:text-green-400 transition-colors",children:"Cancellation"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/safety",className:"text-gray-300 hover:text-green-400 transition-colors",children:"Safety Guidelines"})})]})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2025 TrekInn. All rights reserved."}),(0,r.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,r.jsx)(l(),{href:"/privacy",className:"text-gray-400 hover:text-green-400 text-sm transition-colors",children:"Privacy Policy"}),(0,r.jsx)(l(),{href:"/terms",className:"text-gray-400 hover:text-green-400 text-sm transition-colors",children:"Terms of Service"})]})]})]})})}}}]);