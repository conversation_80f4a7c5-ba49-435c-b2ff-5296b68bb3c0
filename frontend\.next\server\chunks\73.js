exports.id=73,exports.ids=[73],exports.modules={189:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},1135:()=>{},1317:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(687),n=s(5814),o=s.n(n),a=s(8965),i=s(3931),l=s(8340);function c(){return(0,r.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)(a.A,{className:"h-8 w-8 text-green-400"}),(0,r.jsx)("span",{className:"text-xl font-bold",children:"TrekInn"})]}),(0,r.jsx)("p",{className:"text-gray-300 mb-4 max-w-md",children:"Discover the world's most breathtaking trekking adventures. From the Himalayas to the Andes, we offer expertly guided treks that create memories to last a lifetime."}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-gray-300",children:[(0,r.jsx)(i.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"text-sm",children:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-gray-300",children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"text-sm",children:"+1 (555) 123-4567"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Quick Links"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/",className:"text-gray-300 hover:text-green-400 transition-colors",children:"Home"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/treks",className:"text-gray-300 hover:text-green-400 transition-colors",children:"All Treks"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/about",className:"text-gray-300 hover:text-green-400 transition-colors",children:"About Us"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/contact",className:"text-gray-300 hover:text-green-400 transition-colors",children:"Contact"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Support"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/help",className:"text-gray-300 hover:text-green-400 transition-colors",children:"Help Center"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/booking-policy",className:"text-gray-300 hover:text-green-400 transition-colors",children:"Booking Policy"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/cancellation",className:"text-gray-300 hover:text-green-400 transition-colors",children:"Cancellation"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/safety",className:"text-gray-300 hover:text-green-400 transition-colors",children:"Safety Guidelines"})})]})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2025 TrekInn. All rights reserved."}),(0,r.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,r.jsx)(o(),{href:"/privacy",className:"text-gray-400 hover:text-green-400 text-sm transition-colors",children:"Privacy Policy"}),(0,r.jsx)(o(),{href:"/terms",className:"text-gray-400 hover:text-green-400 text-sm transition-colors",children:"Terms of Service"})]})]})]})})}},2185:(e,t,s)=>{"use strict";s.d(t,{PR:()=>o,R2:()=>n,iC:()=>a});let r=s(1060).A.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json",Accept:"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),window.location.href="/login"),Promise.reject(e)));let n={register:e=>r.post("/auth/register",e),login:e=>r.post("/auth/login",e),logout:()=>r.post("/auth/logout"),getUser:()=>r.get("/auth/user")},o={getAll:e=>r.get("/treks",{params:e}),getById:e=>r.get(`/treks/${e}`),create:e=>r.post("/treks",e),update:(e,t)=>r.put(`/treks/${e}`,t),delete:e=>r.delete(`/treks/${e}`)},a={getAll:e=>r.get("/bookings",{params:e}),getById:e=>r.get(`/bookings/${e}`),create:e=>r.post("/bookings",e),update:(e,t)=>r.put(`/bookings/${e}`,t),cancel:e=>r.delete(`/bookings/${e}`)}},2748:(e,t,s)=>{Promise.resolve().then(s.bind(s,3213))},2925:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},3213:(e,t,s)=>{"use strict";s.d(t,{A:()=>l,AuthProvider:()=>i});var r=s(687),n=s(3210),o=s(2185);let a=(0,n.createContext)(void 0);function i({children:e}){let[t,s]=(0,n.useState)(null),[i,l]=(0,n.useState)(null),[c,d]=(0,n.useState)(!0),x=async(e,t)=>{try{let r=(await o.R2.login({email:e,password:t})).data;s(r.user),l(r.token),localStorage.setItem("auth_token",r.token),localStorage.setItem("user",JSON.stringify(r.user))}catch(e){throw e}},m=async(e,t,r,n)=>{try{let a=(await o.R2.register({name:e,email:t,password:r,password_confirmation:n})).data;s(a.user),l(a.token),localStorage.setItem("auth_token",a.token),localStorage.setItem("user",JSON.stringify(a.user))}catch(e){throw e}},h=async()=>{try{i&&await o.R2.logout()}catch(e){console.error("Logout error:",e)}finally{s(null),l(null),localStorage.removeItem("auth_token"),localStorage.removeItem("user")}};return(0,r.jsx)(a.Provider,{value:{user:t,token:i,login:x,register:m,logout:h,loading:c},children:e})}function l(){let e=(0,n.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>c});var r=s(7413),n=s(2376),o=s.n(n),a=s(8726),i=s.n(a);s(1135);var l=s(9131);let c={title:"TrekInn - Adventure Awaits",description:"Discover amazing trekking adventures around the world with TrekInn"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${o().variable} ${i().variable} antialiased`,children:(0,r.jsx)(l.AuthProvider,{children:e})})})}},4604:(e,t,s)=>{Promise.resolve().then(s.bind(s,9131))},9131:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>n});var r=s(2907);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\contexts\\AuthContext.tsx","useAuth")},9190:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(687),n=s(5814),o=s.n(n),a=s(3213),i=s(8965),l=s(83),c=s(1860),d=s(2941),x=s(3210);function m(){let{user:e,logout:t}=(0,a.A)(),[s,n]=(0,x.useState)(!1),m=()=>{t(),n(!1)};return(0,r.jsx)("nav",{className:"bg-white shadow-lg sticky top-0 z-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(o(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)(i.A,{className:"h-8 w-8 text-green-600"}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"TrekInn"})]})}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[(0,r.jsx)(o(),{href:"/",className:"text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Home"}),(0,r.jsx)(o(),{href:"/treks",className:"text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Treks"}),e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o(),{href:"/dashboard",className:"text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Dashboard"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-gray-700 text-sm",children:["Welcome, ",e.name]}),(0,r.jsxs)("button",{onClick:m,className:"flex items-center space-x-1 text-gray-700 hover:text-red-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Logout"})]})]})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(o(),{href:"/login",className:"text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Login"}),(0,r.jsx)(o(),{href:"/register",className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",children:"Sign Up"})]})]}),(0,r.jsx)("div",{className:"md:hidden flex items-center",children:(0,r.jsx)("button",{onClick:()=>n(!s),className:"text-gray-700 hover:text-green-600 p-2",children:s?(0,r.jsx)(c.A,{className:"h-6 w-6"}):(0,r.jsx)(d.A,{className:"h-6 w-6"})})})]}),s&&(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t",children:[(0,r.jsx)(o(),{href:"/",className:"text-gray-700 hover:text-green-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>n(!1),children:"Home"}),(0,r.jsx)(o(),{href:"/treks",className:"text-gray-700 hover:text-green-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>n(!1),children:"Treks"}),e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o(),{href:"/dashboard",className:"text-gray-700 hover:text-green-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>n(!1),children:"Dashboard"}),(0,r.jsx)("div",{className:"px-3 py-2",children:(0,r.jsxs)("span",{className:"text-gray-700 text-sm",children:["Welcome, ",e.name]})}),(0,r.jsx)("button",{onClick:m,className:"text-gray-700 hover:text-red-600 block px-3 py-2 rounded-md text-base font-medium w-full text-left",children:"Logout"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o(),{href:"/login",className:"text-gray-700 hover:text-green-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>n(!1),children:"Login"}),(0,r.jsx)(o(),{href:"/register",className:"bg-green-600 hover:bg-green-700 text-white block px-3 py-2 rounded-md text-base font-medium mx-3",onClick:()=>n(!1),children:"Sign Up"})]})]})})]})})}}};