<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Trek;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class TrekController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Trek::where('is_active', true);

        // Search functionality
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%");
            });
        }

        // Filter by difficulty
        if ($request->has('difficulty')) {
            $query->where('difficulty', $request->get('difficulty'));
        }

        // Filter by price range
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->get('min_price'));
        }
        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->get('max_price'));
        }

        // Filter by duration
        if ($request->has('max_duration')) {
            $query->where('duration_days', '<=', $request->get('max_duration'));
        }

        // Sort options
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, ['price', 'duration_days', 'rating', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $treks = $query->paginate($request->get('per_page', 12));

        return response()->json($treks);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'detailed_description' => 'nullable|string',
            'location' => 'required|string|max:255',
            'duration_days' => 'required|integer|min:1',
            'difficulty' => 'required|in:easy,moderate,difficult,extreme',
            'price' => 'required|numeric|min:0',
            'max_participants' => 'required|integer|min:1',
            'start_date' => 'required|date|after:today',
            'end_date' => 'required|date|after:start_date',
            'included_items' => 'nullable|array',
            'required_items' => 'nullable|array',
            'image_url' => 'nullable|url',
            'gallery_images' => 'nullable|array',
        ]);

        $validated['available_spots'] = $validated['max_participants'];

        $trek = Trek::create($validated);

        return response()->json([
            'message' => 'Trek created successfully',
            'trek' => $trek,
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Trek $trek): JsonResponse
    {
        $trek->load('bookings');

        return response()->json([
            'trek' => $trek,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Trek $trek): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'detailed_description' => 'nullable|string',
            'location' => 'sometimes|string|max:255',
            'duration_days' => 'sometimes|integer|min:1',
            'difficulty' => 'sometimes|in:easy,moderate,difficult,extreme',
            'price' => 'sometimes|numeric|min:0',
            'max_participants' => 'sometimes|integer|min:1',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after:start_date',
            'included_items' => 'nullable|array',
            'required_items' => 'nullable|array',
            'image_url' => 'nullable|url',
            'gallery_images' => 'nullable|array',
            'is_active' => 'sometimes|boolean',
        ]);

        $trek->update($validated);

        return response()->json([
            'message' => 'Trek updated successfully',
            'trek' => $trek,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Trek $trek): JsonResponse
    {
        // Soft delete by setting is_active to false
        $trek->update(['is_active' => false]);

        return response()->json([
            'message' => 'Trek deactivated successfully',
        ]);
    }
}
