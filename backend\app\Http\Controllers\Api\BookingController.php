<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Trek;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class BookingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $bookings = $request->user()
            ->bookings()
            ->with('trek')
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 10));

        return response()->json($bookings);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'trek_id' => 'required|exists:treks,id',
            'participants_count' => 'required|integer|min:1',
            'booking_date' => 'required|date|after:today',
            'participant_details' => 'nullable|array',
            'special_requirements' => 'nullable|string',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:20',
        ]);

        $trek = Trek::findOrFail($validated['trek_id']);

        // Check if trek has enough available spots
        if (!$trek->hasAvailableSpots($validated['participants_count'])) {
            return response()->json([
                'message' => 'Not enough available spots for this trek',
                'available_spots' => $trek->available_spots,
            ], 422);
        }

        // Check if booking date is within trek period
        $bookingDate = \Carbon\Carbon::parse($validated['booking_date']);
        if ($bookingDate->lt($trek->start_date) || $bookingDate->gt($trek->end_date)) {
            return response()->json([
                'message' => 'Booking date must be within trek period',
                'trek_start_date' => $trek->start_date,
                'trek_end_date' => $trek->end_date,
            ], 422);
        }

        DB::beginTransaction();

        try {
            // Calculate total amount
            $totalAmount = $trek->price * $validated['participants_count'];

            // Create booking
            $booking = Booking::create([
                'user_id' => $request->user()->id,
                'trek_id' => $validated['trek_id'],
                'participants_count' => $validated['participants_count'],
                'total_amount' => $totalAmount,
                'booking_date' => $validated['booking_date'],
                'participant_details' => $validated['participant_details'] ?? [],
                'special_requirements' => $validated['special_requirements'],
                'emergency_contact_name' => $validated['emergency_contact_name'],
                'emergency_contact_phone' => $validated['emergency_contact_phone'],
            ]);

            // Reduce available spots
            $trek->reduceAvailableSpots($validated['participants_count']);

            DB::commit();

            $booking->load('trek');

            return response()->json([
                'message' => 'Booking created successfully',
                'booking' => $booking,
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'message' => 'Failed to create booking',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, Booking $booking): JsonResponse
    {
        // Ensure user can only see their own bookings
        if ($booking->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        $booking->load('trek');

        return response()->json([
            'booking' => $booking,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Booking $booking): JsonResponse
    {
        // Ensure user can only update their own bookings
        if ($booking->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        $validated = $request->validate([
            'participant_details' => 'nullable|array',
            'special_requirements' => 'nullable|string',
            'emergency_contact_name' => 'sometimes|string|max:255',
            'emergency_contact_phone' => 'sometimes|string|max:20',
        ]);

        $booking->update($validated);

        return response()->json([
            'message' => 'Booking updated successfully',
            'booking' => $booking,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, Booking $booking): JsonResponse
    {
        // Ensure user can only cancel their own bookings
        if ($booking->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        if (!$booking->canBeCancelled()) {
            return response()->json([
                'message' => 'This booking cannot be cancelled',
            ], 422);
        }

        $booking->cancel();

        return response()->json([
            'message' => 'Booking cancelled successfully',
        ]);
    }
}
