(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[130],{2095:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>p});var r=a(5155),t=a(2115),n=a(5695),l=a(5731),i=a(283),c=a(5494),d=a(6821),o=a(7550),m=a(5339),x=a(9074),g=a(7580);let u=(0,a(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);function p(){let e=(0,n.useParams)(),s=(0,n.useRouter)(),{user:a}=(0,i.A)(),[p,h]=(0,t.useState)(null),[b,y]=(0,t.useState)(!0),[j,f]=(0,t.useState)(!1),[N,v]=(0,t.useState)(""),[_,k]=(0,t.useState)({participants_count:1,booking_date:"",special_requirements:"",emergency_contact_name:"",emergency_contact_phone:"",participant_details:[{name:"",age:"",dietary_requirements:""}]});(0,t.useEffect)(()=>{if(!a)return void s.push("/login");let r=async()=>{try{let s=await l.PR.getById(e.trekId);h(s.data.trek),k(e=>({...e,booking_date:s.data.trek.start_date}))}catch(e){v("Trek not found"),console.error("Error fetching trek:",e)}finally{y(!1)}};e.trekId&&r()},[e.trekId,a,s]);let w=e=>{let{name:s,value:a}=e.target;k(e=>({...e,[s]:a}))},A=e=>{let s=Array.from({length:e},(e,s)=>_.participant_details[s]||{name:"",age:"",dietary_requirements:""});k(a=>({...a,participants_count:e,participant_details:s}))},q=(e,s,a)=>{let r=[..._.participant_details];r[e]={...r[e],[s]:a},k(e=>({...e,participant_details:r}))},C=async a=>{a.preventDefault(),f(!0),v("");try{let a={trek_id:e.trekId,participants_count:_.participants_count,booking_date:_.booking_date,participant_details:_.participant_details,special_requirements:_.special_requirements,emergency_contact_name:_.emergency_contact_name,emergency_contact_phone:_.emergency_contact_phone};await l.iC.create(a),s.push("/dashboard?booking=success")}catch(e){e instanceof Error?v(e.message):v("Booking failed. Please try again.")}finally{f(!1)}};if(b)return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(c.A,{}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-300 rounded mb-8"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-300 rounded mb-4"}),(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"h-12 bg-gray-300 rounded"},e))})]})]})}),(0,r.jsx)(d.A,{})]});if(N||!p)return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(c.A,{}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Booking Error"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:N||"Something went wrong"}),(0,r.jsxs)("button",{onClick:()=>s.back(),className:"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold inline-flex items-center",children:[(0,r.jsx)(o.A,{className:"mr-2 h-5 w-5"}),"Go Back"]})]}),(0,r.jsx)(d.A,{})]});let P=parseFloat(p.price)*_.participants_count;return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(c.A,{}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("button",{onClick:()=>s.back(),className:"text-green-600 hover:text-green-700 inline-flex items-center mb-4",children:[(0,r.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Back to trek details"]}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Book Your Adventure"}),(0,r.jsxs)("p",{className:"text-gray-600 mt-2",children:["Complete your booking for ",p.title]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("form",{onSubmit:C,className:"space-y-6",children:[N&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md flex items-center",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2"}),N]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Trip Details"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Number of Participants"}),(0,r.jsx)("select",{value:_.participants_count,onChange:e=>A(parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",required:!0,children:[...Array(Math.min(p.available_spots,8))].map((e,s)=>(0,r.jsxs)("option",{value:s+1,children:[s+1," participant",0!==s?"s":""]},s+1))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Preferred Start Date"}),(0,r.jsx)("input",{type:"date",name:"booking_date",value:_.booking_date,onChange:w,min:p.start_date,max:p.end_date,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",required:!0})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Participant Information"}),_.participant_details.map((e,s)=>(0,r.jsxs)("div",{className:"mb-6 p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("h3",{className:"font-semibold text-gray-900 mb-3",children:["Participant ",s+1]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name"}),(0,r.jsx)("input",{type:"text",value:e.name,onChange:e=>q(s,"name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Age"}),(0,r.jsx)("input",{type:"number",min:"16",max:"80",value:e.age,onChange:e=>q(s,"age",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Dietary Requirements"}),(0,r.jsx)("input",{type:"text",value:e.dietary_requirements,onChange:e=>q(s,"dietary_requirements",e.target.value),placeholder:"e.g., Vegetarian, Allergies",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"})]})]})]},s))]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Emergency Contact"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Contact Name"}),(0,r.jsx)("input",{type:"text",name:"emergency_contact_name",value:_.emergency_contact_name,onChange:w,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Contact Phone"}),(0,r.jsx)("input",{type:"tel",name:"emergency_contact_phone",value:_.emergency_contact_phone,onChange:w,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",required:!0})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Special Requirements"}),(0,r.jsx)("textarea",{name:"special_requirements",value:_.special_requirements,onChange:w,rows:4,placeholder:"Any special requirements, medical conditions, or requests...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsx)("button",{type:"submit",disabled:j,className:"w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-semibold text-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:j?"Processing Booking...":"Book Now - $".concat(P.toFixed(2))})]})}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 sticky top-8",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Booking Summary"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:p.title}),(0,r.jsx)("p",{className:"text-gray-600",children:p.location})]}),(0,r.jsxs)("div",{className:"border-t pt-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Price per person:"}),(0,r.jsxs)("span",{className:"font-semibold",children:["$",p.price]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Participants:"}),(0,r.jsx)("span",{className:"font-semibold",children:_.participants_count})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center text-lg font-bold border-t pt-2",children:[(0,r.jsx)("span",{children:"Total:"}),(0,r.jsxs)("span",{children:["$",P.toFixed(2)]})]})]}),(0,r.jsxs)("div",{className:"border-t pt-4 text-sm text-gray-600",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),(0,r.jsxs)("span",{children:["Duration: ",p.duration_days," days"]})]}),(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),(0,r.jsxs)("span",{children:["Max group: ",p.max_participants]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u,{className:"h-4 w-4 mr-2"}),(0,r.jsx)("span",{children:"Free cancellation up to 48h"})]})]})]})]})})]})]}),(0,r.jsx)(d.A,{})]})}},4842:(e,s,a)=>{Promise.resolve().then(a.bind(a,2095))},5339:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5695:(e,s,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(s,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(s,{useRouter:function(){return r.useRouter}})},7550:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7580:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9074:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[464,988,202,441,684,358],()=>s(4842)),_N_E=e.O()}]);