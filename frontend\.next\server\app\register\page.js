(()=>{var e={};e.id=454,e.ids=[454],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1820:e=>{"use strict";e.exports=require("os")},2412:e=>{"use strict";e.exports=require("assert")},2597:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3861:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4530:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\SaaS Course\\\\Augment\\\\trekInn\\\\frontend\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\register\\page.tsx","default")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6167:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var s=t(687),a=t(3210),n=t(6189),o=t(5814),i=t.n(o),l=t(3213),d=t(9190),c=t(1317),u=t(8965),m=t(2597),p=t(3861);function x(){let[e,r]=(0,a.useState)({name:"",email:"",password:"",passwordConfirmation:""}),[t,o]=(0,a.useState)(!1),[x,h]=(0,a.useState)(!1),[g,f]=(0,a.useState)(!1),[y,b]=(0,a.useState)(""),{register:v}=(0,l.A)(),w=(0,n.useRouter)(),j=t=>{r({...e,[t.target.name]:t.target.value})},N=async r=>{if(r.preventDefault(),f(!0),b(""),e.password!==e.passwordConfirmation){b("Passwords do not match"),f(!1);return}try{await v(e.name,e.email,e.password,e.passwordConfirmation),w.push("/dashboard")}catch(e){e instanceof Error?b(e.message):b("Registration failed. Please try again.")}finally{f(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(d.A,{}),(0,s.jsx)("div",{className:"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(u.A,{className:"h-12 w-12 text-green-600"})}),(0,s.jsx)("h2",{className:"mt-6 text-3xl font-bold text-gray-900",children:"Join TrekInn"}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Create your account and start your adventure today"})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:N,children:[y&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md",children:y}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,s.jsx)("input",{id:"name",name:"name",type:"text",autoComplete:"name",required:!0,value:e.name,onChange:j,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"Enter your full name"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:j,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"Enter your email"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{id:"password",name:"password",type:t?"text":"password",autoComplete:"new-password",required:!0,value:e.password,onChange:j,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 pr-10",placeholder:"Create a password"}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>o(!t),children:t?(0,s.jsx)(m.A,{className:"h-5 w-5 text-gray-400"}):(0,s.jsx)(p.A,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"passwordConfirmation",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{id:"passwordConfirmation",name:"passwordConfirmation",type:x?"text":"password",autoComplete:"new-password",required:!0,value:e.passwordConfirmation,onChange:j,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 pr-10",placeholder:"Confirm your password"}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>h(!x),children:x?(0,s.jsx)(m.A,{className:"h-5 w-5 text-gray-400"}):(0,s.jsx)(p.A,{className:"h-5 w-5 text-gray-400"})})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"terms",name:"terms",type:"checkbox",required:!0,className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"}),(0,s.jsxs)("label",{htmlFor:"terms",className:"ml-2 block text-sm text-gray-900",children:["I agree to the"," ",(0,s.jsx)(i(),{href:"/terms",className:"text-green-600 hover:text-green-500",children:"Terms of Service"})," ","and"," ",(0,s.jsx)(i(),{href:"/privacy",className:"text-green-600 hover:text-green-500",children:"Privacy Policy"})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:g,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed",children:g?"Creating account...":"Create account"})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,s.jsx)(i(),{href:"/login",className:"font-medium text-green-600 hover:text-green-500",children:"Sign in here"})]})})]})]})}),(0,s.jsx)(c.A,{})]})}},6189:(e,r,t)=>{"use strict";var s=t(5773);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},7101:(e,r,t)=>{Promise.resolve().then(t.bind(t,6167))},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9831:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(5239),a=t(8088),n=t(8170),o=t.n(n),i=t(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4530)),"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\register\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\register\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9837:(e,r,t)=>{Promise.resolve().then(t.bind(t,4530))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,60,83,73],()=>t(9831));module.exports=s})();