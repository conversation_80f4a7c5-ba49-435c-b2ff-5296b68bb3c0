export interface User {
  id: number;
  name: string;
  email: string;
  email_verified_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Trek {
  id: number;
  title: string;
  description: string;
  detailed_description?: string;
  location: string;
  duration_days: number;
  difficulty: 'easy' | 'moderate' | 'difficult' | 'extreme';
  price: string;
  max_participants: number;
  available_spots: number;
  start_date: string;
  end_date: string;
  included_items?: string[];
  required_items?: string[];
  image_url?: string;
  gallery_images?: string[];
  is_active: boolean;
  rating: string;
  total_reviews: number;
  created_at: string;
  updated_at: string;
}

export interface Booking {
  id: number;
  user_id: number;
  trek_id: number;
  booking_reference: string;
  participants_count: number;
  total_amount: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  booking_date: string;
  participant_details?: Record<string, unknown>[];
  special_requirements?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  payment_status: 'pending' | 'paid' | 'refunded';
  payment_method?: string;
  payment_reference?: string;
  payment_date?: string;
  created_at: string;
  updated_at: string;
  trek?: Trek;
  user?: User;
}

export interface PaginatedResponse<T> {
  current_page: number;
  data: T[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: Array<{
    url: string | null;
    label: string;
    active: boolean;
  }>;
  next_page_url: string | null;
  path: string;
  per_page: number;
  prev_page_url: string | null;
  to: number;
  total: number;
}

export interface AuthResponse {
  message: string;
  user: User;
  token: string;
  token_type: string;
}

export interface ApiError {
  message: string;
  errors?: Record<string, string[]>;
}
