[{"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\layout.tsx": "1", "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\page.tsx": "2", "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\components\\Footer.tsx": "3", "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\components\\Navbar.tsx": "4", "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\contexts\\AuthContext.tsx": "5", "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\lib\\api.ts": "6", "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\types\\index.ts": "7", "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\booking\\[trekId]\\page.tsx": "8", "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\dashboard\\page.tsx": "9", "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\login\\page.tsx": "10", "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\register\\page.tsx": "11", "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\treks\\page.tsx": "12", "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\treks\\[id]\\page.tsx": "13"}, {"size": 842, "mtime": 1749227161726, "results": "14", "hashOfConfig": "15"}, {"size": 7894, "mtime": 1749227635582, "results": "16", "hashOfConfig": "15"}, {"size": 4032, "mtime": 1749227669939, "results": "17", "hashOfConfig": "15"}, {"size": 5603, "mtime": 1749227646704, "results": "18", "hashOfConfig": "15"}, {"size": 2752, "mtime": 1749227070722, "results": "19", "hashOfConfig": "15"}, {"size": 2065, "mtime": 1749227976964, "results": "20", "hashOfConfig": "15"}, {"size": 1893, "mtime": 1749227991524, "results": "21", "hashOfConfig": "15"}, {"size": 15649, "mtime": 1749228499502, "results": "22", "hashOfConfig": "15"}, {"size": 11591, "mtime": 1749228346021, "results": "23", "hashOfConfig": "15"}, {"size": 5836, "mtime": 1749228146179, "results": "24", "hashOfConfig": "15"}, {"size": 8436, "mtime": 1749228181550, "results": "25", "hashOfConfig": "15"}, {"size": 16035, "mtime": 1749228513188, "results": "26", "hashOfConfig": "15"}, {"size": 11667, "mtime": 1749228281702, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "quzccd", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\layout.tsx", [], [], "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\page.tsx", [], [], "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\components\\Footer.tsx", [], [], "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\components\\Navbar.tsx", [], [], "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\lib\\api.ts", [], [], "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\types\\index.ts", [], [], "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\booking\\[trekId]\\page.tsx", [], [], "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\dashboard\\page.tsx", [], [], "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\login\\page.tsx", [], [], "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\register\\page.tsx", [], [], "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\treks\\page.tsx", [], ["67"], "D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\treks\\[id]\\page.tsx", [], [], {"ruleId": "68", "severity": 1, "message": "69", "line": 54, "column": 6, "nodeType": "70", "endLine": 54, "endColumn": 27, "suggestions": "71", "suppressions": "72"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchTreks'. Either include it or remove the dependency array.", "ArrayExpression", ["73"], ["74"], {"desc": "75", "fix": "76"}, {"kind": "77", "justification": "78"}, "Update the dependencies array to be: [searchTerm, filters, fetchTreks]", {"range": "79", "text": "80"}, "directive", "", [1488, 1509], "[searchTerm, filters, fetchTreks]"]