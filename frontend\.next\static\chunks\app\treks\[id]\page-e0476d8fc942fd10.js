(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[484],{475:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>k});var l=a(5155),t=a(2115),r=a(5695),i=a(6766),c=a(6874),d=a.n(c),n=a(5731),x=a(283),m=a(5494),o=a(6821),h=a(7550),g=a(4516),p=a(8564),u=a(9074),j=a(7580),b=a(646),y=a(9946);let N=(0,y.A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]);var f=a(4186),v=a(5868);let w=(0,y.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);function k(){let e=(0,r.useParams)(),s=(0,r.useRouter)(),{user:a}=(0,x.A)(),[c,y]=(0,t.useState)(null),[k,A]=(0,t.useState)(!0),[_,M]=(0,t.useState)("");(0,t.useEffect)(()=>{let s=async()=>{try{let s=await n.PR.getById(e.id);y(s.data.trek)}catch(e){M("Trek not found"),console.error("Error fetching trek:",e)}finally{A(!1)}};e.id&&s()},[e.id]);let q=e=>{switch(e){case"easy":return"bg-green-100 text-green-800";case"moderate":return"bg-yellow-100 text-yellow-800";case"difficult":return"bg-orange-100 text-orange-800";case"extreme":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return k?(0,l.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,l.jsx)(m.A,{}),(0,l.jsxs)("div",{className:"animate-pulse",children:[(0,l.jsx)("div",{className:"h-96 bg-gray-300"}),(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,l.jsxs)("div",{className:"lg:col-span-2",children:[(0,l.jsx)("div",{className:"h-8 bg-gray-300 rounded mb-4"}),(0,l.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-2"}),(0,l.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-4"}),(0,l.jsx)("div",{className:"h-32 bg-gray-300 rounded"})]}),(0,l.jsx)("div",{className:"h-64 bg-gray-300 rounded"})]})})]}),(0,l.jsx)(o.A,{})]}):_||!c?(0,l.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,l.jsx)(m.A,{}),(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Trek Not Found"}),(0,l.jsx)("p",{className:"text-gray-600 mb-8",children:"The trek you're looking for doesn't exist or has been removed."}),(0,l.jsxs)(d(),{href:"/treks",className:"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold inline-flex items-center",children:[(0,l.jsx)(h.A,{className:"mr-2 h-5 w-5"}),"Back to Treks"]})]}),(0,l.jsx)(o.A,{})]}):(0,l.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,l.jsx)(m.A,{}),(0,l.jsxs)("div",{className:"relative h-96",children:[(0,l.jsx)(i.default,{src:c.image_url||"/placeholder-trek.jpg",alt:c.title,fill:!0,className:"object-cover"}),(0,l.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-40"}),(0,l.jsx)("div",{className:"absolute bottom-8 left-8",children:(0,l.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-semibold ".concat(q(c.difficulty)),children:c.difficulty.charAt(0).toUpperCase()+c.difficulty.slice(1)})})]}),(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,l.jsx)("div",{className:"mb-6",children:(0,l.jsxs)(d(),{href:"/treks",className:"text-green-600 hover:text-green-700 inline-flex items-center mb-4",children:[(0,l.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Back to all treks"]})}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,l.jsxs)("div",{className:"lg:col-span-2",children:[(0,l.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:c.title}),(0,l.jsxs)("div",{className:"flex items-center text-gray-600 mb-6",children:[(0,l.jsx)(g.A,{className:"h-5 w-5 mr-2"}),(0,l.jsx)("span",{className:"text-lg",children:c.location})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-6 mb-8",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(p.A,{className:"h-5 w-5 text-yellow-400 mr-1"}),(0,l.jsx)("span",{className:"font-semibold",children:c.rating}),(0,l.jsxs)("span",{className:"text-gray-600 ml-1",children:["(",c.total_reviews," reviews)"]})]}),(0,l.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,l.jsx)(u.A,{className:"h-5 w-5 mr-1"}),(0,l.jsxs)("span",{children:[c.duration_days," days"]})]}),(0,l.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,l.jsx)(j.A,{className:"h-5 w-5 mr-1"}),(0,l.jsxs)("span",{children:[c.available_spots," spots available"]})]})]}),(0,l.jsxs)("div",{className:"prose max-w-none mb-8",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"About This Trek"}),(0,l.jsx)("p",{className:"text-gray-700 text-lg leading-relaxed mb-6",children:c.description}),c.detailed_description&&(0,l.jsx)("div",{className:"text-gray-700 leading-relaxed",children:c.detailed_description.split("\n").map((e,s)=>(0,l.jsx)("p",{className:"mb-4",children:e},s))})]}),c.included_items&&c.included_items.length>0&&(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsxs)("h3",{className:"text-xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,l.jsx)(b.A,{className:"h-6 w-6 text-green-600 mr-2"}),"What's Included"]}),(0,l.jsx)("ul",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:c.included_items.map((e,s)=>(0,l.jsxs)("li",{className:"flex items-center text-gray-700",children:[(0,l.jsx)(b.A,{className:"h-4 w-4 text-green-600 mr-2 flex-shrink-0"}),e]},s))})]}),c.required_items&&c.required_items.length>0&&(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsxs)("h3",{className:"text-xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,l.jsx)(N,{className:"h-6 w-6 text-blue-600 mr-2"}),"What to Bring"]}),(0,l.jsx)("ul",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:c.required_items.map((e,s)=>(0,l.jsxs)("li",{className:"flex items-center text-gray-700",children:[(0,l.jsx)(N,{className:"h-4 w-4 text-blue-600 mr-2 flex-shrink-0"}),e]},s))})]})]}),(0,l.jsx)("div",{className:"lg:col-span-1",children:(0,l.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg shadow-lg p-6 sticky top-8",children:[(0,l.jsxs)("div",{className:"text-center mb-6",children:[(0,l.jsxs)("div",{className:"text-3xl font-bold text-gray-900 mb-2",children:["$",c.price,(0,l.jsx)("span",{className:"text-lg font-normal text-gray-600",children:" / person"})]}),(0,l.jsxs)("div",{className:"text-sm text-gray-600",children:[c.duration_days," day adventure"]})]}),(0,l.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between py-2 border-b border-gray-100",children:[(0,l.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,l.jsx)(f.A,{className:"h-4 w-4 mr-2"}),(0,l.jsx)("span",{children:"Duration"})]}),(0,l.jsxs)("span",{className:"font-semibold",children:[c.duration_days," days"]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between py-2 border-b border-gray-100",children:[(0,l.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,l.jsx)(j.A,{className:"h-4 w-4 mr-2"}),(0,l.jsx)("span",{children:"Group Size"})]}),(0,l.jsxs)("span",{className:"font-semibold",children:["Max ",c.max_participants]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between py-2 border-b border-gray-100",children:[(0,l.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,l.jsx)(u.A,{className:"h-4 w-4 mr-2"}),(0,l.jsx)("span",{children:"Available"})]}),(0,l.jsxs)("span",{className:"font-semibold",children:[c.available_spots," spots"]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,l.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,l.jsx)(v.A,{className:"h-4 w-4 mr-2"}),(0,l.jsx)("span",{children:"Difficulty"})]}),(0,l.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-semibold ".concat(q(c.difficulty)),children:c.difficulty.charAt(0).toUpperCase()+c.difficulty.slice(1)})]})]}),c.available_spots>0?(0,l.jsx)("button",{onClick:()=>{if(!a)return void s.push("/login");s.push("/booking/".concat(null==c?void 0:c.id))},className:"w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-semibold text-lg transition-colors",children:a?"Book Now":"Login to Book"}):(0,l.jsx)("button",{disabled:!0,className:"w-full bg-gray-400 text-white py-3 px-4 rounded-lg font-semibold text-lg cursor-not-allowed",children:"Fully Booked"}),(0,l.jsx)("div",{className:"mt-4 text-center",children:(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Free cancellation up to 48 hours before departure"})}),c.available_spots<=5&&c.available_spots>0&&(0,l.jsx)("div",{className:"mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg",children:(0,l.jsxs)("div",{className:"flex items-center text-orange-800",children:[(0,l.jsx)(w,{className:"h-4 w-4 mr-2"}),(0,l.jsxs)("span",{className:"text-sm font-medium",children:["Only ",c.available_spots," spot",1!==c.available_spots?"s":""," left!"]})]})})]})})]})]}),(0,l.jsx)(o.A,{})]})}},646:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1048:(e,s,a)=>{Promise.resolve().then(a.bind(a,475))},4186:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5695:(e,s,a)=>{"use strict";var l=a(8999);a.o(l,"useParams")&&a.d(s,{useParams:function(){return l.useParams}}),a.o(l,"useRouter")&&a.d(s,{useRouter:function(){return l.useRouter}})},5868:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},7550:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[464,988,384,202,441,684,358],()=>s(1048)),_N_E=e.O()}]);