<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Trek extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'detailed_description',
        'location',
        'duration_days',
        'difficulty',
        'price',
        'max_participants',
        'available_spots',
        'start_date',
        'end_date',
        'included_items',
        'required_items',
        'image_url',
        'gallery_images',
        'is_active',
        'rating',
        'total_reviews',
    ];

    protected $casts = [
        'included_items' => 'array',
        'required_items' => 'array',
        'gallery_images' => 'array',
        'start_date' => 'date',
        'end_date' => 'date',
        'price' => 'decimal:2',
        'rating' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the bookings for the trek.
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Check if trek has available spots
     */
    public function hasAvailableSpots(int $requestedSpots = 1): bool
    {
        return $this->available_spots >= $requestedSpots;
    }

    /**
     * Reduce available spots when booking is made
     */
    public function reduceAvailableSpots(int $spots): void
    {
        $this->decrement('available_spots', $spots);
    }

    /**
     * Increase available spots when booking is cancelled
     */
    public function increaseAvailableSpots(int $spots): void
    {
        $this->increment('available_spots', $spots);
    }
}
