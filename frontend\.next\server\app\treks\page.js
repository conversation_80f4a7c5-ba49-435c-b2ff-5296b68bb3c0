(()=>{var e={};e.id=550,e.ids=[550],e.modules={334:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(2688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1328:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\SaaS Course\\\\Augment\\\\trekInn\\\\frontend\\\\src\\\\app\\\\treks\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\treks\\page.tsx","default")},1630:e=>{"use strict";e.exports=require("http")},1667:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>o});var t=s(5239),a=s(8088),l=s(8170),i=s.n(l),n=s(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(r,d);let o={children:["",{children:["treks",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1328)),"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\treks\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\web\\SaaS Course\\Augment\\trekInn\\frontend\\src\\app\\treks\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/treks/page",pathname:"/treks",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},1820:e=>{"use strict";e.exports=require("os")},2412:e=>{"use strict";e.exports=require("assert")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3485:(e,r,s)=>{Promise.resolve().then(s.bind(s,1328))},3629:(e,r,s)=>{Promise.resolve().then(s.bind(s,8459))},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},8459:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>j});var t=s(687),a=s(3210),l=s(5814),i=s.n(l),n=s(474),d=s(2185),o=s(9190),c=s(1317),x=s(2688);let u=(0,x.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),m=(0,x.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);var p=s(7992),g=s(228),h=s(1312),b=s(4398),f=s(334);function j(){let[e,r]=(0,a.useState)(null),[s,l]=(0,a.useState)(!0),[x,j]=(0,a.useState)(""),[y,v]=(0,a.useState)({difficulty:"",min_price:"",max_price:"",max_duration:"",sort_by:"created_at",sort_order:"desc"}),[N,w]=(0,a.useState)(!1),_=async(e=1)=>{l(!0);try{let s={page:e,per_page:12,search:x,...y};Object.keys(s).forEach(e=>{""===s[e]&&delete s[e]});let t=await d.PR.getAll(s);r(t.data)}catch(e){console.error("Error fetching treks:",e)}finally{l(!1)}},k=(e,r)=>{v(s=>({...s,[e]:r}))},A=()=>{v({difficulty:"",min_price:"",max_price:"",max_duration:"",sort_by:"created_at",sort_order:"desc"}),j("")},C=e=>{switch(e){case"easy":return"bg-green-100 text-green-800";case"moderate":return"bg-yellow-100 text-yellow-800";case"difficult":return"bg-orange-100 text-orange-800";case"extreme":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return(0,t.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,t.jsx)(o.A,{}),(0,t.jsx)("div",{className:"bg-green-600 text-white py-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:"Discover Amazing Treks"}),(0,t.jsx)("p",{className:"text-xl text-green-100 max-w-2xl mx-auto",children:"Explore our collection of carefully curated trekking adventures from around the world"})]})})}),(0,t.jsx)("div",{className:"bg-gray-50 py-8",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsx)("form",{onSubmit:e=>{e.preventDefault(),_()},className:"mb-6",children:(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)(u,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,t.jsx)("input",{type:"text",placeholder:"Search treks by name, location, or description...",value:x,onChange:e=>j(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"})]}),(0,t.jsxs)("button",{type:"button",onClick:()=>w(!N),className:"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50",children:[(0,t.jsx)(m,{className:"h-5 w-5"}),"Filters"]})]})}),N&&(0,t.jsxs)("div",{className:"border-t pt-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Difficulty"}),(0,t.jsxs)("select",{value:y.difficulty,onChange:e=>k("difficulty",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",children:[(0,t.jsx)("option",{value:"",children:"All Difficulties"}),(0,t.jsx)("option",{value:"easy",children:"Easy"}),(0,t.jsx)("option",{value:"moderate",children:"Moderate"}),(0,t.jsx)("option",{value:"difficult",children:"Difficult"}),(0,t.jsx)("option",{value:"extreme",children:"Extreme"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Min Price ($)"}),(0,t.jsx)("input",{type:"number",placeholder:"0",value:y.min_price,onChange:e=>k("min_price",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Price ($)"}),(0,t.jsx)("input",{type:"number",placeholder:"10000",value:y.max_price,onChange:e=>k("max_price",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Duration (days)"}),(0,t.jsx)("input",{type:"number",placeholder:"30",value:y.max_duration,onChange:e=>k("max_duration",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"})]})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sort by"}),(0,t.jsxs)("select",{value:y.sort_by,onChange:e=>k("sort_by",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",children:[(0,t.jsx)("option",{value:"created_at",children:"Newest"}),(0,t.jsx)("option",{value:"price",children:"Price"}),(0,t.jsx)("option",{value:"duration_days",children:"Duration"}),(0,t.jsx)("option",{value:"rating",children:"Rating"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Order"}),(0,t.jsxs)("select",{value:y.sort_order,onChange:e=>k("sort_order",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500",children:[(0,t.jsx)("option",{value:"desc",children:"Descending"}),(0,t.jsx)("option",{value:"asc",children:"Ascending"})]})]})]}),(0,t.jsx)("button",{onClick:A,className:"text-green-600 hover:text-green-700 font-medium",children:"Clear all filters"})]})]})]})})}),(0,t.jsx)("div",{className:"py-12",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s?(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[...Array(6)].map((e,r)=>(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden animate-pulse",children:[(0,t.jsx)("div",{className:"h-64 bg-gray-300"}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-300 rounded mb-4"}),(0,t.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-300 rounded"})]})]},r))}):e&&e.data.length>0?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-gray-900",children:[e.total," Trek",1!==e.total?"s":""," Found"]}),(0,t.jsxs)("p",{className:"text-gray-600",children:["Showing ",e.from,"-",e.to," of ",e.total," results"]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.data.map(e=>(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow",children:[(0,t.jsxs)("div",{className:"relative h-64",children:[(0,t.jsx)(n.default,{src:e.image_url||"/placeholder-trek.jpg",alt:e.title,fill:!0,className:"object-cover"}),(0,t.jsxs)("div",{className:"absolute top-4 right-4 bg-white px-2 py-1 rounded-full text-sm font-semibold text-gray-900",children:["$",e.price]}),(0,t.jsx)("div",{className:"absolute top-4 left-4",children:(0,t.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${C(e.difficulty)}`,children:e.difficulty.charAt(0).toUpperCase()+e.difficulty.slice(1)})})]}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:e.title}),(0,t.jsxs)("div",{className:"flex items-center text-gray-600 mb-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-1"}),(0,t.jsx)("span",{className:"text-sm",children:e.location})]}),(0,t.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-2",children:e.description}),(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-1"}),(0,t.jsxs)("span",{children:[e.duration_days," days"]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-1"}),(0,t.jsxs)("span",{children:[e.available_spots," spots"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 text-yellow-400 mr-1"}),(0,t.jsx)("span",{className:"text-sm font-semibold",children:e.rating})]})]}),(0,t.jsxs)(i(),{href:`/treks/${e.id}`,className:"w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-semibold transition-colors inline-flex items-center justify-center",children:["View Details",(0,t.jsx)(f.A,{className:"ml-2 h-4 w-4"})]})]})]},e.id))}),e.last_page>1&&(0,t.jsx)("div",{className:"mt-12 flex justify-center",children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[e.prev_page_url&&(0,t.jsx)("button",{onClick:()=>_(e.current_page-1),className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:"Previous"}),[...Array(Math.min(5,e.last_page))].map((r,s)=>{let a=s+1;return(0,t.jsx)("button",{onClick:()=>_(a),className:`px-4 py-2 border rounded-md ${a===e.current_page?"bg-green-600 text-white border-green-600":"border-gray-300 hover:bg-gray-50"}`,children:a},a)}),e.next_page_url&&(0,t.jsx)("button",{onClick:()=>_(e.current_page+1),className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:"Next"})]})})]}):(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-gray-400 mb-4",children:(0,t.jsx)(u,{className:"h-16 w-16 mx-auto"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No treks found"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Try adjusting your search criteria or filters to find more results."}),(0,t.jsx)("button",{onClick:A,className:"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold",children:"Clear filters"})]})})}),(0,t.jsx)(c.A,{})]})}},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,60,83,680,73],()=>s(1667));module.exports=t})();